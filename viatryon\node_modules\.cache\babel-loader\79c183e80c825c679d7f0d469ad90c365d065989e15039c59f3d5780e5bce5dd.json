{"ast": null, "code": "/**\n * Utility for removing white backgrounds from images\n * Only removes pure white and very close whites to preserve light gray watches\n */\n\n/**\n * Remove white background from an image while preserving watch/bracelet details\n * @param {string} imageSrc - Image source URL or data URL\n * @param {number} tolerance - Color tolerance for white detection (0-255, default: 3)\n * @returns {Promise<string>} - Data URL of processed image\n */\nexport const removeWhiteBackground = async (imageSrc, tolerance = 3) => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n    img.onload = () => {\n      try {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the image\n        ctx.drawImage(img, 0, 0);\n\n        // Get image data\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n\n        // First pass: Identify edge pixels to preserve product boundaries\n        const edgePixels = new Set();\n        for (let y = 1; y < canvas.height - 1; y++) {\n          for (let x = 1; x < canvas.width - 1; x++) {\n            const idx = (y * canvas.width + x) * 4;\n            const r = data[idx];\n            const g = data[idx + 1];\n            const b = data[idx + 2];\n\n            // Check surrounding pixels for edge detection\n            let isEdge = false;\n            for (let dy = -1; dy <= 1; dy++) {\n              for (let dx = -1; dx <= 1; dx++) {\n                if (dx === 0 && dy === 0) continue;\n                const neighborIdx = ((y + dy) * canvas.width + (x + dx)) * 4;\n                const nr = data[neighborIdx];\n                const ng = data[neighborIdx + 1];\n                const nb = data[neighborIdx + 2];\n\n                // If there's a significant color difference, it's an edge\n                const colorDiff = Math.abs(r - nr) + Math.abs(g - ng) + Math.abs(b - nb);\n                if (colorDiff > 30) {\n                  // Reduced threshold for better edge detection\n                  isEdge = true;\n                  break;\n                }\n              }\n              if (isEdge) break;\n            }\n            if (isEdge) {\n              edgePixels.add(idx / 4);\n            }\n          }\n        }\n\n        // Second pass: Remove only white background while preserving all product colors\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n          const pixelIndex = i / 4;\n\n          // Calculate color properties\n          const brightness = (r + g + b) / 3;\n          const saturation = Math.max(r, g, b) - Math.min(r, g, b);\n\n          // Check if pixel is near edges (preserve product boundaries)\n          const isNearEdge = edgePixels.has(pixelIndex);\n\n          // Only remove pure white or near-white pixels that aren't edges\n          const isPureWhite = r > 250 && g > 250 && b > 250 && Math.abs(r - g) < 8 && Math.abs(g - b) < 8 && !isNearEdge;\n\n          // Conservative light gray detection - only for pure background\n          const isLightGrayShadow = brightness > 245 &&\n          // Higher threshold - only very light grays\n          brightness < 250 &&\n          // Not pure white\n          saturation < 8 &&\n          // Very low saturation (pure gray)\n          Math.abs(r - g) < 5 && Math.abs(g - b) < 5 && Math.abs(r - b) < 5 && !isNearEdge;\n\n          // Remove background if it's pure white and not an edge\n          if (isPureWhite) {\n            data[i + 3] = 0; // Make fully transparent\n          } else if (isLightGrayShadow) {\n            // Remove light gray shadows that are common in bracelet images\n            data[i + 3] = 0; // Make fully transparent\n          } else if (isVeryLightShadow) {\n            // Remove very light shadows aggressively\n            data[i + 3] = 0; // Make fully transparent\n          } else if (brightness > 245 && !isNearEdge) {\n            // For very bright but not pure white, reduce opacity slightly\n            data[i + 3] = Math.max(0, data[i + 3] - 30);\n          }\n        }\n\n        // Put the modified image data back\n        ctx.putImageData(imageData, 0, 0);\n\n        // Convert to data URL\n        const processedImageUrl = canvas.toDataURL('image/png');\n        resolve(processedImageUrl);\n      } catch (error) {\n        reject(error);\n      }\n    };\n    img.onerror = () => {\n      reject(new Error('Failed to load image'));\n    };\n    img.src = imageSrc;\n  });\n};\n\n/**\n * Check if a pixel is white or very close to white\n * @param {number} r - Red value (0-255)\n * @param {number} g - Green value (0-255)\n * @param {number} b - Blue value (0-255)\n * @param {number} tolerance - Tolerance for white detection\n * @returns {boolean} - True if pixel is considered white\n */\nconst isWhitePixel = (r, g, b, tolerance) => {\n  // Pure white check\n  if (r === 255 && g === 255 && b === 255) {\n    return true;\n  }\n\n  // Near-white check with tolerance\n  // Only consider pixels that are very close to pure white\n  const whiteThreshold = 255 - tolerance;\n  return r >= whiteThreshold && g >= whiteThreshold && b >= whiteThreshold;\n};\n\n/**\n * Check if a pixel is part of an edge\n * @param {Uint8ClampedArray} data - Image data\n * @param {number} index - Pixel index\n * @param {number} width - Image width\n * @returns {boolean} - True if pixel is part of an edge\n */\nconst isEdgePixel = (data, index, width) => {\n  const pixelSize = 4;\n  const height = data.length / (width * pixelSize);\n  const x = index / pixelSize % width;\n  const y = Math.floor(index / pixelSize / width);\n\n  // Skip edge pixels of the image\n  if (x === 0 || x === width - 1 || y === 0 || y === height - 1) {\n    return true;\n  }\n\n  // Check surrounding pixels for non-white colors\n  const surroundingPixels = [data[index - width * pixelSize - pixelSize],\n  // top-left\n  data[index - width * pixelSize],\n  // top\n  data[index - width * pixelSize + pixelSize],\n  // top-right\n  data[index - pixelSize],\n  // left\n  data[index + pixelSize],\n  // right\n  data[index + width * pixelSize - pixelSize],\n  // bottom-left\n  data[index + width * pixelSize],\n  // bottom\n  data[index + width * pixelSize + pixelSize] // bottom-right\n  ];\n\n  // If any surrounding pixel is not white, this is an edge pixel\n  return surroundingPixels.some((pixel, i) => {\n    if (pixel === undefined) return false;\n    const r = data[index - width * pixelSize - pixelSize + i * pixelSize];\n    const g = data[index - width * pixelSize - pixelSize + i * pixelSize + 1];\n    const b = data[index - width * pixelSize - pixelSize + i * pixelSize + 2];\n    return !isWhitePixel(r, g, b, 5);\n  });\n};\n\n/**\n * Batch process multiple images to remove white backgrounds\n * @param {string[]} imageSrcs - Array of image source URLs\n * @param {number} tolerance - Color tolerance for white detection\n * @returns {Promise<string[]>} - Array of processed image data URLs\n */\nexport const batchRemoveWhiteBackground = async (imageSrcs, tolerance = 10) => {\n  const promises = imageSrcs.map(src => removeWhiteBackground(src, tolerance));\n  return Promise.all(promises);\n};\n\n/**\n * Process an image file and return processed data URL\n * @param {File} file - Image file\n * @param {number} tolerance - Color tolerance for white detection\n * @returns {Promise<string>} - Processed image data URL\n */\nexport const processImageFile = async (file, tolerance = 10) => {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onload = async e => {\n      try {\n        const processedImage = await removeWhiteBackground(e.target.result, tolerance);\n        resolve(processedImage);\n      } catch (error) {\n        reject(error);\n      }\n    };\n    reader.onerror = () => {\n      reject(new Error('Failed to read file'));\n    };\n    reader.readAsDataURL(file);\n  });\n};\n\n/**\n * Specialized background removal for watches and bracelets\n * @param {string} imageSrc - Image source URL or data URL\n * @param {string} productType - 'watch' or 'bracelet'\n * @returns {Promise<string>} - Data URL of processed image\n */\nexport const removeProductBackground = async (imageSrc, productType = 'watch') => {\n  try {\n    if (productType === 'bracelet') {\n      // Use specialized bracelet background removal\n      return await removeBraceletBackground(imageSrc);\n    } else {\n      // For watches: use conservative tolerance to preserve dial details\n      return await removeWhiteBackground(imageSrc, 2);\n    }\n  } catch (error) {\n    console.warn(`Failed to remove background for ${productType}:`, error);\n    return imageSrc; // Return original if processing fails\n  }\n};\n\n/**\n * Specialized background removal for bracelets with aggressive shadow removal\n * @param {string} imageSrc - Image source URL or data URL\n * @returns {Promise<string>} - Data URL of processed image\n */\nexport const removeBraceletBackground = async imageSrc => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n    img.onload = () => {\n      try {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the image\n        ctx.drawImage(img, 0, 0);\n\n        // Get image data\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n\n        // First pass: Identify bracelet pixels (darker, more saturated areas)\n        const braceletPixels = new Set();\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n          const brightness = (r + g + b) / 3;\n          const saturation = Math.max(r, g, b) - Math.min(r, g, b);\n\n          // Consider pixel as bracelet if it's darker or has color\n          const isBraceletPixel = brightness < 200 ||\n          // Darker areas\n          saturation > 20 ||\n          // Colored areas\n          r < 180 || g < 180 || b < 180 // Any non-light color\n          ;\n          if (isBraceletPixel) {\n            braceletPixels.add(i / 4);\n          }\n        }\n\n        // Second pass: Remove everything that's not clearly bracelet\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n          const pixelIndex = i / 4;\n          const brightness = (r + g + b) / 3;\n          const saturation = Math.max(r, g, b) - Math.min(r, g, b);\n\n          // Check if this pixel or nearby pixels are bracelet\n          const isBraceletArea = braceletPixels.has(pixelIndex) || braceletPixels.has(pixelIndex - 1) || braceletPixels.has(pixelIndex + 1) || braceletPixels.has(pixelIndex - canvas.width) || braceletPixels.has(pixelIndex + canvas.width);\n\n          // Aggressive background removal for bracelets\n          const shouldRemove =\n          // Remove white and light gray areas\n          brightness > 210 && saturation < 25 ||\n          // Remove light shadows (common in bracelet images)\n          brightness > 180 && brightness < 220 && saturation < 15 ||\n          // Remove very light areas that aren't near bracelet\n          brightness > 200 && !isBraceletArea;\n          if (shouldRemove) {\n            data[i + 3] = 0; // Make fully transparent\n          }\n        }\n\n        // Put the modified image data back\n        ctx.putImageData(imageData, 0, 0);\n\n        // Convert to data URL\n        const processedImageUrl = canvas.toDataURL('image/png');\n        resolve(processedImageUrl);\n      } catch (error) {\n        reject(error);\n      }\n    };\n    img.onerror = () => {\n      reject(new Error('Failed to load image'));\n    };\n    img.src = imageSrc;\n  });\n};\n\n/**\n * Check if an image has a white background\n * @param {string} imageSrc - Image source URL\n * @returns {Promise<boolean>} - True if image has significant white background\n */\nexport const hasWhiteBackground = async imageSrc => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n    img.onload = () => {\n      try {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        canvas.width = img.width;\n        canvas.height = img.height;\n        ctx.drawImage(img, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        let whitePixelCount = 0;\n        const totalPixels = data.length / 4;\n        for (let i = 0; i < data.length; i += 4) {\n          const red = data[i];\n          const green = data[i + 1];\n          const blue = data[i + 2];\n          if (isWhitePixel(red, green, blue, 10)) {\n            whitePixelCount++;\n          }\n        }\n\n        // Consider image to have white background if more than 30% pixels are white\n        const whitePercentage = whitePixelCount / totalPixels * 100;\n        resolve(whitePercentage > 30);\n      } catch (error) {\n        reject(error);\n      }\n    };\n    img.onerror = () => {\n      reject(new Error('Failed to load image'));\n    };\n    img.src = imageSrc;\n  });\n};", "map": {"version": 3, "names": ["removeWhiteBackground", "imageSrc", "tolerance", "Promise", "resolve", "reject", "img", "Image", "crossOrigin", "onload", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "drawImage", "imageData", "getImageData", "data", "edgePixels", "Set", "y", "x", "idx", "r", "g", "b", "isEdge", "dy", "dx", "neighborIdx", "nr", "ng", "nb", "colorDiff", "Math", "abs", "add", "i", "length", "pixelIndex", "brightness", "saturation", "max", "min", "isNearEdge", "has", "isPureWhite", "isLightGrayShadow", "isVeryLightShadow", "putImageData", "processedImageUrl", "toDataURL", "error", "onerror", "Error", "src", "isWhitePixel", "whiteT<PERSON>eshold", "isEdgePixel", "index", "pixelSize", "floor", "surroundingPixels", "some", "pixel", "undefined", "batchRemoveWhiteBackground", "imageSrcs", "promises", "map", "all", "processImageFile", "file", "reader", "FileReader", "e", "processedImage", "target", "result", "readAsDataURL", "removeProductBackground", "productType", "removeBraceletBackground", "console", "warn", "braceletPixels", "isBraceletPixel", "isBraceletArea", "<PERSON><PERSON><PERSON><PERSON>", "hasWhiteBackground", "whitePixelCount", "totalPixels", "red", "green", "blue", "whitePercentage"], "sources": ["D:/Via/test/viatryon/src/utils/backgroundRemover.js"], "sourcesContent": ["/**\n * Utility for removing white backgrounds from images\n * Only removes pure white and very close whites to preserve light gray watches\n */\n\n/**\n * Remove white background from an image while preserving watch/bracelet details\n * @param {string} imageSrc - Image source URL or data URL\n * @param {number} tolerance - Color tolerance for white detection (0-255, default: 3)\n * @returns {Promise<string>} - Data URL of processed image\n */\nexport const removeWhiteBackground = async (imageSrc, tolerance = 3) => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n\n    img.onload = () => {\n      try {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the image\n        ctx.drawImage(img, 0, 0);\n\n        // Get image data\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n\n        // First pass: Identify edge pixels to preserve product boundaries\n        const edgePixels = new Set();\n        for (let y = 1; y < canvas.height - 1; y++) {\n          for (let x = 1; x < canvas.width - 1; x++) {\n            const idx = (y * canvas.width + x) * 4;\n            const r = data[idx];\n            const g = data[idx + 1];\n            const b = data[idx + 2];\n\n            // Check surrounding pixels for edge detection\n            let isEdge = false;\n            for (let dy = -1; dy <= 1; dy++) {\n              for (let dx = -1; dx <= 1; dx++) {\n                if (dx === 0 && dy === 0) continue;\n                const neighborIdx = ((y + dy) * canvas.width + (x + dx)) * 4;\n                const nr = data[neighborIdx];\n                const ng = data[neighborIdx + 1];\n                const nb = data[neighborIdx + 2];\n\n                // If there's a significant color difference, it's an edge\n                const colorDiff = Math.abs(r - nr) + Math.abs(g - ng) + Math.abs(b - nb);\n                if (colorDiff > 30) { // Reduced threshold for better edge detection\n                  isEdge = true;\n                  break;\n                }\n              }\n              if (isEdge) break;\n            }\n\n            if (isEdge) {\n              edgePixels.add(idx / 4);\n            }\n          }\n        }\n\n        // Second pass: Remove only white background while preserving all product colors\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n          const pixelIndex = i / 4;\n\n          // Calculate color properties\n          const brightness = (r + g + b) / 3;\n          const saturation = Math.max(r, g, b) - Math.min(r, g, b);\n\n          // Check if pixel is near edges (preserve product boundaries)\n          const isNearEdge = edgePixels.has(pixelIndex);\n\n          // Only remove pure white or near-white pixels that aren't edges\n          const isPureWhite = (\n            r > 250 &&\n            g > 250 &&\n            b > 250 &&\n            Math.abs(r - g) < 8 &&\n            Math.abs(g - b) < 8 &&\n            !isNearEdge\n          );\n\n          // Conservative light gray detection - only for pure background\n          const isLightGrayShadow = (\n            brightness > 245 && // Higher threshold - only very light grays\n            brightness < 250 && // Not pure white\n            saturation < 8 && // Very low saturation (pure gray)\n            Math.abs(r - g) < 5 &&\n            Math.abs(g - b) < 5 &&\n            Math.abs(r - b) < 5 &&\n            !isNearEdge\n          );\n\n          // Remove background if it's pure white and not an edge\n          if (isPureWhite) {\n            data[i + 3] = 0; // Make fully transparent\n          } else if (isLightGrayShadow) {\n            // Remove light gray shadows that are common in bracelet images\n            data[i + 3] = 0; // Make fully transparent\n          } else if (isVeryLightShadow) {\n            // Remove very light shadows aggressively\n            data[i + 3] = 0; // Make fully transparent\n          } else if (brightness > 245 && !isNearEdge) {\n            // For very bright but not pure white, reduce opacity slightly\n            data[i + 3] = Math.max(0, data[i + 3] - 30);\n          }\n        }\n\n        // Put the modified image data back\n        ctx.putImageData(imageData, 0, 0);\n\n        // Convert to data URL\n        const processedImageUrl = canvas.toDataURL('image/png');\n        resolve(processedImageUrl);\n      } catch (error) {\n        reject(error);\n      }\n    };\n\n    img.onerror = () => {\n      reject(new Error('Failed to load image'));\n    };\n\n    img.src = imageSrc;\n  });\n};\n\n/**\n * Check if a pixel is white or very close to white\n * @param {number} r - Red value (0-255)\n * @param {number} g - Green value (0-255)\n * @param {number} b - Blue value (0-255)\n * @param {number} tolerance - Tolerance for white detection\n * @returns {boolean} - True if pixel is considered white\n */\nconst isWhitePixel = (r, g, b, tolerance) => {\n  // Pure white check\n  if (r === 255 && g === 255 && b === 255) {\n    return true;\n  }\n\n  // Near-white check with tolerance\n  // Only consider pixels that are very close to pure white\n  const whiteThreshold = 255 - tolerance;\n  return r >= whiteThreshold && g >= whiteThreshold && b >= whiteThreshold;\n};\n\n/**\n * Check if a pixel is part of an edge\n * @param {Uint8ClampedArray} data - Image data\n * @param {number} index - Pixel index\n * @param {number} width - Image width\n * @returns {boolean} - True if pixel is part of an edge\n */\nconst isEdgePixel = (data, index, width) => {\n  const pixelSize = 4;\n  const height = data.length / (width * pixelSize);\n  const x = (index / pixelSize) % width;\n  const y = Math.floor((index / pixelSize) / width);\n\n  // Skip edge pixels of the image\n  if (x === 0 || x === width - 1 || y === 0 || y === height - 1) {\n    return true;\n  }\n\n  // Check surrounding pixels for non-white colors\n  const surroundingPixels = [\n    data[index - width * pixelSize - pixelSize], // top-left\n    data[index - width * pixelSize], // top\n    data[index - width * pixelSize + pixelSize], // top-right\n    data[index - pixelSize], // left\n    data[index + pixelSize], // right\n    data[index + width * pixelSize - pixelSize], // bottom-left\n    data[index + width * pixelSize], // bottom\n    data[index + width * pixelSize + pixelSize] // bottom-right\n  ];\n\n  // If any surrounding pixel is not white, this is an edge pixel\n  return surroundingPixels.some((pixel, i) => {\n    if (pixel === undefined) return false;\n    const r = data[index - width * pixelSize - pixelSize + (i * pixelSize)];\n    const g = data[index - width * pixelSize - pixelSize + (i * pixelSize) + 1];\n    const b = data[index - width * pixelSize - pixelSize + (i * pixelSize) + 2];\n    return !isWhitePixel(r, g, b, 5);\n  });\n};\n\n/**\n * Batch process multiple images to remove white backgrounds\n * @param {string[]} imageSrcs - Array of image source URLs\n * @param {number} tolerance - Color tolerance for white detection\n * @returns {Promise<string[]>} - Array of processed image data URLs\n */\nexport const batchRemoveWhiteBackground = async (imageSrcs, tolerance = 10) => {\n  const promises = imageSrcs.map(src => removeWhiteBackground(src, tolerance));\n  return Promise.all(promises);\n};\n\n/**\n * Process an image file and return processed data URL\n * @param {File} file - Image file\n * @param {number} tolerance - Color tolerance for white detection\n * @returns {Promise<string>} - Processed image data URL\n */\nexport const processImageFile = async (file, tolerance = 10) => {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n\n    reader.onload = async (e) => {\n      try {\n        const processedImage = await removeWhiteBackground(e.target.result, tolerance);\n        resolve(processedImage);\n      } catch (error) {\n        reject(error);\n      }\n    };\n\n    reader.onerror = () => {\n      reject(new Error('Failed to read file'));\n    };\n\n    reader.readAsDataURL(file);\n  });\n};\n\n/**\n * Specialized background removal for watches and bracelets\n * @param {string} imageSrc - Image source URL or data URL\n * @param {string} productType - 'watch' or 'bracelet'\n * @returns {Promise<string>} - Data URL of processed image\n */\nexport const removeProductBackground = async (imageSrc, productType = 'watch') => {\n  try {\n    if (productType === 'bracelet') {\n      // Use specialized bracelet background removal\n      return await removeBraceletBackground(imageSrc);\n    } else {\n      // For watches: use conservative tolerance to preserve dial details\n      return await removeWhiteBackground(imageSrc, 2);\n    }\n  } catch (error) {\n    console.warn(`Failed to remove background for ${productType}:`, error);\n    return imageSrc; // Return original if processing fails\n  }\n};\n\n/**\n * Specialized background removal for bracelets with aggressive shadow removal\n * @param {string} imageSrc - Image source URL or data URL\n * @returns {Promise<string>} - Data URL of processed image\n */\nexport const removeBraceletBackground = async (imageSrc) => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n\n    img.onload = () => {\n      try {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the image\n        ctx.drawImage(img, 0, 0);\n\n        // Get image data\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n\n        // First pass: Identify bracelet pixels (darker, more saturated areas)\n        const braceletPixels = new Set();\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n\n          const brightness = (r + g + b) / 3;\n          const saturation = Math.max(r, g, b) - Math.min(r, g, b);\n\n          // Consider pixel as bracelet if it's darker or has color\n          const isBraceletPixel = (\n            brightness < 200 || // Darker areas\n            saturation > 20 || // Colored areas\n            (r < 180 || g < 180 || b < 180) // Any non-light color\n          );\n\n          if (isBraceletPixel) {\n            braceletPixels.add(i / 4);\n          }\n        }\n\n        // Second pass: Remove everything that's not clearly bracelet\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n          const pixelIndex = i / 4;\n\n          const brightness = (r + g + b) / 3;\n          const saturation = Math.max(r, g, b) - Math.min(r, g, b);\n\n          // Check if this pixel or nearby pixels are bracelet\n          const isBraceletArea = braceletPixels.has(pixelIndex) ||\n            braceletPixels.has(pixelIndex - 1) ||\n            braceletPixels.has(pixelIndex + 1) ||\n            braceletPixels.has(pixelIndex - canvas.width) ||\n            braceletPixels.has(pixelIndex + canvas.width);\n\n          // Aggressive background removal for bracelets\n          const shouldRemove = (\n            // Remove white and light gray areas\n            (brightness > 210 && saturation < 25) ||\n            // Remove light shadows (common in bracelet images)\n            (brightness > 180 && brightness < 220 && saturation < 15) ||\n            // Remove very light areas that aren't near bracelet\n            (brightness > 200 && !isBraceletArea)\n          );\n\n          if (shouldRemove) {\n            data[i + 3] = 0; // Make fully transparent\n          }\n        }\n\n        // Put the modified image data back\n        ctx.putImageData(imageData, 0, 0);\n\n        // Convert to data URL\n        const processedImageUrl = canvas.toDataURL('image/png');\n        resolve(processedImageUrl);\n      } catch (error) {\n        reject(error);\n      }\n    };\n\n    img.onerror = () => {\n      reject(new Error('Failed to load image'));\n    };\n\n    img.src = imageSrc;\n  });\n};\n\n/**\n * Check if an image has a white background\n * @param {string} imageSrc - Image source URL\n * @returns {Promise<boolean>} - True if image has significant white background\n */\nexport const hasWhiteBackground = async (imageSrc) => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n\n    img.onload = () => {\n      try {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n\n        canvas.width = img.width;\n        canvas.height = img.height;\n        ctx.drawImage(img, 0, 0);\n\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n\n        let whitePixelCount = 0;\n        const totalPixels = data.length / 4;\n\n        for (let i = 0; i < data.length; i += 4) {\n          const red = data[i];\n          const green = data[i + 1];\n          const blue = data[i + 2];\n\n          if (isWhitePixel(red, green, blue, 10)) {\n            whitePixelCount++;\n          }\n        }\n\n        // Consider image to have white background if more than 30% pixels are white\n        const whitePercentage = (whitePixelCount / totalPixels) * 100;\n        resolve(whitePercentage > 30);\n      } catch (error) {\n        reject(error);\n      }\n    };\n\n    img.onerror = () => {\n      reject(new Error('Failed to load image'));\n    };\n\n    img.src = imageSrc;\n  });\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,qBAAqB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,SAAS,GAAG,CAAC,KAAK;EACtE,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,WAAW,GAAG,WAAW;IAE7BF,GAAG,CAACG,MAAM,GAAG,MAAM;MACjB,IAAI;QACF,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;QAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;QAEnCJ,MAAM,CAACK,KAAK,GAAGT,GAAG,CAACS,KAAK;QACxBL,MAAM,CAACM,MAAM,GAAGV,GAAG,CAACU,MAAM;;QAE1B;QACAH,GAAG,CAACI,SAAS,CAACX,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;;QAExB;QACA,MAAMY,SAAS,GAAGL,GAAG,CAACM,YAAY,CAAC,CAAC,EAAE,CAAC,EAAET,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACM,MAAM,CAAC;QACrE,MAAMI,IAAI,GAAGF,SAAS,CAACE,IAAI;;QAE3B;QACA,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;QAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,MAAM,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,EAAE,EAAE;UAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,MAAM,CAACK,KAAK,GAAG,CAAC,EAAES,CAAC,EAAE,EAAE;YACzC,MAAMC,GAAG,GAAG,CAACF,CAAC,GAAGb,MAAM,CAACK,KAAK,GAAGS,CAAC,IAAI,CAAC;YACtC,MAAME,CAAC,GAAGN,IAAI,CAACK,GAAG,CAAC;YACnB,MAAME,CAAC,GAAGP,IAAI,CAACK,GAAG,GAAG,CAAC,CAAC;YACvB,MAAMG,CAAC,GAAGR,IAAI,CAACK,GAAG,GAAG,CAAC,CAAC;;YAEvB;YACA,IAAII,MAAM,GAAG,KAAK;YAClB,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;cAC/B,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;gBAC/B,IAAIA,EAAE,KAAK,CAAC,IAAID,EAAE,KAAK,CAAC,EAAE;gBAC1B,MAAME,WAAW,GAAG,CAAC,CAACT,CAAC,GAAGO,EAAE,IAAIpB,MAAM,CAACK,KAAK,IAAIS,CAAC,GAAGO,EAAE,CAAC,IAAI,CAAC;gBAC5D,MAAME,EAAE,GAAGb,IAAI,CAACY,WAAW,CAAC;gBAC5B,MAAME,EAAE,GAAGd,IAAI,CAACY,WAAW,GAAG,CAAC,CAAC;gBAChC,MAAMG,EAAE,GAAGf,IAAI,CAACY,WAAW,GAAG,CAAC,CAAC;;gBAEhC;gBACA,MAAMI,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACZ,CAAC,GAAGO,EAAE,CAAC,GAAGI,IAAI,CAACC,GAAG,CAACX,CAAC,GAAGO,EAAE,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACV,CAAC,GAAGO,EAAE,CAAC;gBACxE,IAAIC,SAAS,GAAG,EAAE,EAAE;kBAAE;kBACpBP,MAAM,GAAG,IAAI;kBACb;gBACF;cACF;cACA,IAAIA,MAAM,EAAE;YACd;YAEA,IAAIA,MAAM,EAAE;cACVR,UAAU,CAACkB,GAAG,CAACd,GAAG,GAAG,CAAC,CAAC;YACzB;UACF;QACF;;QAEA;QACA,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,CAACqB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;UACvC,MAAMd,CAAC,GAAGN,IAAI,CAACoB,CAAC,CAAC;UACjB,MAAMb,CAAC,GAAGP,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC;UACrB,MAAMZ,CAAC,GAAGR,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC;UACrB,MAAME,UAAU,GAAGF,CAAC,GAAG,CAAC;;UAExB;UACA,MAAMG,UAAU,GAAG,CAACjB,CAAC,GAAGC,CAAC,GAAGC,CAAC,IAAI,CAAC;UAClC,MAAMgB,UAAU,GAAGP,IAAI,CAACQ,GAAG,CAACnB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGS,IAAI,CAACS,GAAG,CAACpB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;;UAExD;UACA,MAAMmB,UAAU,GAAG1B,UAAU,CAAC2B,GAAG,CAACN,UAAU,CAAC;;UAE7C;UACA,MAAMO,WAAW,GACfvB,CAAC,GAAG,GAAG,IACPC,CAAC,GAAG,GAAG,IACPC,CAAC,GAAG,GAAG,IACPS,IAAI,CAACC,GAAG,CAACZ,CAAC,GAAGC,CAAC,CAAC,GAAG,CAAC,IACnBU,IAAI,CAACC,GAAG,CAACX,CAAC,GAAGC,CAAC,CAAC,GAAG,CAAC,IACnB,CAACmB,UACF;;UAED;UACA,MAAMG,iBAAiB,GACrBP,UAAU,GAAG,GAAG;UAAI;UACpBA,UAAU,GAAG,GAAG;UAAI;UACpBC,UAAU,GAAG,CAAC;UAAI;UAClBP,IAAI,CAACC,GAAG,CAACZ,CAAC,GAAGC,CAAC,CAAC,GAAG,CAAC,IACnBU,IAAI,CAACC,GAAG,CAACX,CAAC,GAAGC,CAAC,CAAC,GAAG,CAAC,IACnBS,IAAI,CAACC,GAAG,CAACZ,CAAC,GAAGE,CAAC,CAAC,GAAG,CAAC,IACnB,CAACmB,UACF;;UAED;UACA,IAAIE,WAAW,EAAE;YACf7B,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UACnB,CAAC,MAAM,IAAIU,iBAAiB,EAAE;YAC5B;YACA9B,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UACnB,CAAC,MAAM,IAAIW,iBAAiB,EAAE;YAC5B;YACA/B,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UACnB,CAAC,MAAM,IAAIG,UAAU,GAAG,GAAG,IAAI,CAACI,UAAU,EAAE;YAC1C;YACA3B,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC,GAAGH,IAAI,CAACQ,GAAG,CAAC,CAAC,EAAEzB,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;UAC7C;QACF;;QAEA;QACA3B,GAAG,CAACuC,YAAY,CAAClC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEjC;QACA,MAAMmC,iBAAiB,GAAG3C,MAAM,CAAC4C,SAAS,CAAC,WAAW,CAAC;QACvDlD,OAAO,CAACiD,iBAAiB,CAAC;MAC5B,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdlD,MAAM,CAACkD,KAAK,CAAC;MACf;IACF,CAAC;IAEDjD,GAAG,CAACkD,OAAO,GAAG,MAAM;MAClBnD,MAAM,CAAC,IAAIoD,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC3C,CAAC;IAEDnD,GAAG,CAACoD,GAAG,GAAGzD,QAAQ;EACpB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0D,YAAY,GAAGA,CAACjC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE1B,SAAS,KAAK;EAC3C;EACA,IAAIwB,CAAC,KAAK,GAAG,IAAIC,CAAC,KAAK,GAAG,IAAIC,CAAC,KAAK,GAAG,EAAE;IACvC,OAAO,IAAI;EACb;;EAEA;EACA;EACA,MAAMgC,cAAc,GAAG,GAAG,GAAG1D,SAAS;EACtC,OAAOwB,CAAC,IAAIkC,cAAc,IAAIjC,CAAC,IAAIiC,cAAc,IAAIhC,CAAC,IAAIgC,cAAc;AAC1E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAACzC,IAAI,EAAE0C,KAAK,EAAE/C,KAAK,KAAK;EAC1C,MAAMgD,SAAS,GAAG,CAAC;EACnB,MAAM/C,MAAM,GAAGI,IAAI,CAACqB,MAAM,IAAI1B,KAAK,GAAGgD,SAAS,CAAC;EAChD,MAAMvC,CAAC,GAAIsC,KAAK,GAAGC,SAAS,GAAIhD,KAAK;EACrC,MAAMQ,CAAC,GAAGc,IAAI,CAAC2B,KAAK,CAAEF,KAAK,GAAGC,SAAS,GAAIhD,KAAK,CAAC;;EAEjD;EACA,IAAIS,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAKT,KAAK,GAAG,CAAC,IAAIQ,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAKP,MAAM,GAAG,CAAC,EAAE;IAC7D,OAAO,IAAI;EACb;;EAEA;EACA,MAAMiD,iBAAiB,GAAG,CACxB7C,IAAI,CAAC0C,KAAK,GAAG/C,KAAK,GAAGgD,SAAS,GAAGA,SAAS,CAAC;EAAE;EAC7C3C,IAAI,CAAC0C,KAAK,GAAG/C,KAAK,GAAGgD,SAAS,CAAC;EAAE;EACjC3C,IAAI,CAAC0C,KAAK,GAAG/C,KAAK,GAAGgD,SAAS,GAAGA,SAAS,CAAC;EAAE;EAC7C3C,IAAI,CAAC0C,KAAK,GAAGC,SAAS,CAAC;EAAE;EACzB3C,IAAI,CAAC0C,KAAK,GAAGC,SAAS,CAAC;EAAE;EACzB3C,IAAI,CAAC0C,KAAK,GAAG/C,KAAK,GAAGgD,SAAS,GAAGA,SAAS,CAAC;EAAE;EAC7C3C,IAAI,CAAC0C,KAAK,GAAG/C,KAAK,GAAGgD,SAAS,CAAC;EAAE;EACjC3C,IAAI,CAAC0C,KAAK,GAAG/C,KAAK,GAAGgD,SAAS,GAAGA,SAAS,CAAC,CAAC;EAAA,CAC7C;;EAED;EACA,OAAOE,iBAAiB,CAACC,IAAI,CAAC,CAACC,KAAK,EAAE3B,CAAC,KAAK;IAC1C,IAAI2B,KAAK,KAAKC,SAAS,EAAE,OAAO,KAAK;IACrC,MAAM1C,CAAC,GAAGN,IAAI,CAAC0C,KAAK,GAAG/C,KAAK,GAAGgD,SAAS,GAAGA,SAAS,GAAIvB,CAAC,GAAGuB,SAAU,CAAC;IACvE,MAAMpC,CAAC,GAAGP,IAAI,CAAC0C,KAAK,GAAG/C,KAAK,GAAGgD,SAAS,GAAGA,SAAS,GAAIvB,CAAC,GAAGuB,SAAU,GAAG,CAAC,CAAC;IAC3E,MAAMnC,CAAC,GAAGR,IAAI,CAAC0C,KAAK,GAAG/C,KAAK,GAAGgD,SAAS,GAAGA,SAAS,GAAIvB,CAAC,GAAGuB,SAAU,GAAG,CAAC,CAAC;IAC3E,OAAO,CAACJ,YAAY,CAACjC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC;EAClC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMyC,0BAA0B,GAAG,MAAAA,CAAOC,SAAS,EAAEpE,SAAS,GAAG,EAAE,KAAK;EAC7E,MAAMqE,QAAQ,GAAGD,SAAS,CAACE,GAAG,CAACd,GAAG,IAAI1D,qBAAqB,CAAC0D,GAAG,EAAExD,SAAS,CAAC,CAAC;EAC5E,OAAOC,OAAO,CAACsE,GAAG,CAACF,QAAQ,CAAC;AAC9B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,gBAAgB,GAAG,MAAAA,CAAOC,IAAI,EAAEzE,SAAS,GAAG,EAAE,KAAK;EAC9D,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMuE,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAE/BD,MAAM,CAACnE,MAAM,GAAG,MAAOqE,CAAC,IAAK;MAC3B,IAAI;QACF,MAAMC,cAAc,GAAG,MAAM/E,qBAAqB,CAAC8E,CAAC,CAACE,MAAM,CAACC,MAAM,EAAE/E,SAAS,CAAC;QAC9EE,OAAO,CAAC2E,cAAc,CAAC;MACzB,CAAC,CAAC,OAAOxB,KAAK,EAAE;QACdlD,MAAM,CAACkD,KAAK,CAAC;MACf;IACF,CAAC;IAEDqB,MAAM,CAACpB,OAAO,GAAG,MAAM;MACrBnD,MAAM,CAAC,IAAIoD,KAAK,CAAC,qBAAqB,CAAC,CAAC;IAC1C,CAAC;IAEDmB,MAAM,CAACM,aAAa,CAACP,IAAI,CAAC;EAC5B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,uBAAuB,GAAG,MAAAA,CAAOlF,QAAQ,EAAEmF,WAAW,GAAG,OAAO,KAAK;EAChF,IAAI;IACF,IAAIA,WAAW,KAAK,UAAU,EAAE;MAC9B;MACA,OAAO,MAAMC,wBAAwB,CAACpF,QAAQ,CAAC;IACjD,CAAC,MAAM;MACL;MACA,OAAO,MAAMD,qBAAqB,CAACC,QAAQ,EAAE,CAAC,CAAC;IACjD;EACF,CAAC,CAAC,OAAOsD,KAAK,EAAE;IACd+B,OAAO,CAACC,IAAI,CAAC,mCAAmCH,WAAW,GAAG,EAAE7B,KAAK,CAAC;IACtE,OAAOtD,QAAQ,CAAC,CAAC;EACnB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMoF,wBAAwB,GAAG,MAAOpF,QAAQ,IAAK;EAC1D,OAAO,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,WAAW,GAAG,WAAW;IAE7BF,GAAG,CAACG,MAAM,GAAG,MAAM;MACjB,IAAI;QACF,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;QAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;QAEnCJ,MAAM,CAACK,KAAK,GAAGT,GAAG,CAACS,KAAK;QACxBL,MAAM,CAACM,MAAM,GAAGV,GAAG,CAACU,MAAM;;QAE1B;QACAH,GAAG,CAACI,SAAS,CAACX,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;;QAExB;QACA,MAAMY,SAAS,GAAGL,GAAG,CAACM,YAAY,CAAC,CAAC,EAAE,CAAC,EAAET,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACM,MAAM,CAAC;QACrE,MAAMI,IAAI,GAAGF,SAAS,CAACE,IAAI;;QAE3B;QACA,MAAMoE,cAAc,GAAG,IAAIlE,GAAG,CAAC,CAAC;QAChC,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,CAACqB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;UACvC,MAAMd,CAAC,GAAGN,IAAI,CAACoB,CAAC,CAAC;UACjB,MAAMb,CAAC,GAAGP,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC;UACrB,MAAMZ,CAAC,GAAGR,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC;UAErB,MAAMG,UAAU,GAAG,CAACjB,CAAC,GAAGC,CAAC,GAAGC,CAAC,IAAI,CAAC;UAClC,MAAMgB,UAAU,GAAGP,IAAI,CAACQ,GAAG,CAACnB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGS,IAAI,CAACS,GAAG,CAACpB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;;UAExD;UACA,MAAM6D,eAAe,GACnB9C,UAAU,GAAG,GAAG;UAAI;UACpBC,UAAU,GAAG,EAAE;UAAI;UAClBlB,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAI,CAAC;UACjC;UAED,IAAI6D,eAAe,EAAE;YACnBD,cAAc,CAACjD,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC;UAC3B;QACF;;QAEA;QACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,CAACqB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;UACvC,MAAMd,CAAC,GAAGN,IAAI,CAACoB,CAAC,CAAC;UACjB,MAAMb,CAAC,GAAGP,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC;UACrB,MAAMZ,CAAC,GAAGR,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC;UACrB,MAAME,UAAU,GAAGF,CAAC,GAAG,CAAC;UAExB,MAAMG,UAAU,GAAG,CAACjB,CAAC,GAAGC,CAAC,GAAGC,CAAC,IAAI,CAAC;UAClC,MAAMgB,UAAU,GAAGP,IAAI,CAACQ,GAAG,CAACnB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGS,IAAI,CAACS,GAAG,CAACpB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;;UAExD;UACA,MAAM8D,cAAc,GAAGF,cAAc,CAACxC,GAAG,CAACN,UAAU,CAAC,IACnD8C,cAAc,CAACxC,GAAG,CAACN,UAAU,GAAG,CAAC,CAAC,IAClC8C,cAAc,CAACxC,GAAG,CAACN,UAAU,GAAG,CAAC,CAAC,IAClC8C,cAAc,CAACxC,GAAG,CAACN,UAAU,GAAGhC,MAAM,CAACK,KAAK,CAAC,IAC7CyE,cAAc,CAACxC,GAAG,CAACN,UAAU,GAAGhC,MAAM,CAACK,KAAK,CAAC;;UAE/C;UACA,MAAM4E,YAAY;UAChB;UACChD,UAAU,GAAG,GAAG,IAAIC,UAAU,GAAG,EAAE;UACpC;UACCD,UAAU,GAAG,GAAG,IAAIA,UAAU,GAAG,GAAG,IAAIC,UAAU,GAAG,EAAG;UACzD;UACCD,UAAU,GAAG,GAAG,IAAI,CAAC+C,cACvB;UAED,IAAIC,YAAY,EAAE;YAChBvE,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UACnB;QACF;;QAEA;QACA3B,GAAG,CAACuC,YAAY,CAAClC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEjC;QACA,MAAMmC,iBAAiB,GAAG3C,MAAM,CAAC4C,SAAS,CAAC,WAAW,CAAC;QACvDlD,OAAO,CAACiD,iBAAiB,CAAC;MAC5B,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdlD,MAAM,CAACkD,KAAK,CAAC;MACf;IACF,CAAC;IAEDjD,GAAG,CAACkD,OAAO,GAAG,MAAM;MAClBnD,MAAM,CAAC,IAAIoD,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC3C,CAAC;IAEDnD,GAAG,CAACoD,GAAG,GAAGzD,QAAQ;EACpB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM2F,kBAAkB,GAAG,MAAO3F,QAAQ,IAAK;EACpD,OAAO,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,WAAW,GAAG,WAAW;IAE7BF,GAAG,CAACG,MAAM,GAAG,MAAM;MACjB,IAAI;QACF,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;QAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;QAEnCJ,MAAM,CAACK,KAAK,GAAGT,GAAG,CAACS,KAAK;QACxBL,MAAM,CAACM,MAAM,GAAGV,GAAG,CAACU,MAAM;QAC1BH,GAAG,CAACI,SAAS,CAACX,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;QAExB,MAAMY,SAAS,GAAGL,GAAG,CAACM,YAAY,CAAC,CAAC,EAAE,CAAC,EAAET,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACM,MAAM,CAAC;QACrE,MAAMI,IAAI,GAAGF,SAAS,CAACE,IAAI;QAE3B,IAAIyE,eAAe,GAAG,CAAC;QACvB,MAAMC,WAAW,GAAG1E,IAAI,CAACqB,MAAM,GAAG,CAAC;QAEnC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,CAACqB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;UACvC,MAAMuD,GAAG,GAAG3E,IAAI,CAACoB,CAAC,CAAC;UACnB,MAAMwD,KAAK,GAAG5E,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC;UACzB,MAAMyD,IAAI,GAAG7E,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC;UAExB,IAAImB,YAAY,CAACoC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAE,EAAE,CAAC,EAAE;YACtCJ,eAAe,EAAE;UACnB;QACF;;QAEA;QACA,MAAMK,eAAe,GAAIL,eAAe,GAAGC,WAAW,GAAI,GAAG;QAC7D1F,OAAO,CAAC8F,eAAe,GAAG,EAAE,CAAC;MAC/B,CAAC,CAAC,OAAO3C,KAAK,EAAE;QACdlD,MAAM,CAACkD,KAAK,CAAC;MACf;IACF,CAAC;IAEDjD,GAAG,CAACkD,OAAO,GAAG,MAAM;MAClBnD,MAAM,CAAC,IAAIoD,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC3C,CAAC;IAEDnD,GAAG,CAACoD,GAAG,GAAGzD,QAAQ;EACpB,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}