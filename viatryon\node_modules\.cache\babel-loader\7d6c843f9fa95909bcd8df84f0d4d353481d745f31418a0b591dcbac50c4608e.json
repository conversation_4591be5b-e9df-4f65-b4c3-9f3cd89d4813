{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\Tryon.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { QRCodeSVG } from 'qrcode.react'; // Fix the import\nimport uSha<PERSON><PERSON>utter from '../utils/uShapeCutter';\nimport { removeProductBackground } from '../utils/backgroundRemover';\n\n// Add CSS for range slider styling\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst sliderCSS = `\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    border: none;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-webkit-slider-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n  }\n\n  input[type=\"range\"]::-moz-range-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n    border: none;\n  }\n\n  input[type=\"range\"]:focus {\n    outline: none;\n  }\n\n  input[type=\"range\"]:focus::-webkit-slider-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  input[type=\"range\"]:focus::-moz-range-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  /* Clean Switch styles */\n  .switch-container {\n    position: relative;\n    display: inline-block;\n    width: 40px;\n    height: 22px;\n  }\n\n  .switch-container input {\n    opacity: 0;\n    width: 0;\n    height: 0;\n  }\n\n  .switch-slider {\n    position: absolute;\n    cursor: pointer;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(255, 255, 255, 0.3);\n    transition: .3s;\n    border-radius: 22px;\n    border: 1px solid rgba(255, 255, 255, 0.2);\n  }\n\n  .switch-slider:before {\n    position: absolute;\n    content: \"\";\n    height: 16px;\n    width: 16px;\n    left: 2px;\n    bottom: 2px;\n    background-color: white;\n    transition: .3s;\n    border-radius: 50%;\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\n  }\n\n  input:checked + .switch-slider {\n    background-color: #2D8C88;\n  }\n\n  input:checked + .switch-slider:before {\n    transform: translateX(18px);\n  }\n\n  input:disabled + .switch-slider {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n`;\n\n// Inject CSS (only once)\nif (typeof document !== 'undefined' && !document.getElementById('wrist-size-slider-styles')) {\n  const styleElement = document.createElement('style');\n  styleElement.id = 'wrist-size-slider-styles';\n  styleElement.textContent = sliderCSS;\n  document.head.appendChild(styleElement);\n}\nconst Tryon = ({\n  onBackToHome\n}) => {\n  _s();\n  // Refs for DOM elements\n  const videoRef = useRef(null);\n  const capturedImageRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // State variables\n  const [isCaptured, setIsCaptured] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [isRightHand, setIsRightHand] = useState(false);\n  const [showProductSelection, setShowProductSelection] = useState(false);\n  const [activeTab, setActiveTab] = useState('Watches');\n  const [showHandGuide, setShowHandGuide] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n  const [isDesktop, setIsDesktop] = useState(false);\n  const [showDisclaimerPopup, setShowDisclaimerPopup] = useState(true);\n  const [isUsingModelImage, setIsUsingModelImage] = useState(false);\n\n  // URL parameters state\n  const [urlParams, setUrlParams] = useState({\n    image: null,\n    client: null,\n    size: null,\n    type: null\n  });\n\n  // Analytics tracking state\n  const [sessionId, setSessionId] = useState(null);\n  const [sessionStartTime, setSessionStartTime] = useState(null);\n  const [interactions, setInteractions] = useState([]);\n  const [behaviorMetrics, setBehaviorMetrics] = useState({\n    timeToFirstInteraction: null,\n    productSwitches: 0,\n    backgroundRemovalSuccess: false,\n    handDetectionSuccess: false,\n    totalInteractions: 0,\n    cameraInitSuccess: false,\n    engagementScore: 0,\n    scrollDepth: 0,\n    mouseMovements: 0,\n    hoverEvents: [],\n    gestureEvents: [],\n    featureUsage: {\n      cameraToggle: 0,\n      productRotation: 0,\n      sizeAdjustment: 0,\n      colorChange: 0,\n      screenshot: 0,\n      share: 0,\n      zoom: 0\n    },\n    exitIntent: {\n      detected: false,\n      timestamp: null,\n      beforeConversion: false\n    },\n    attentionMetrics: {\n      focusTime: 0,\n      blurEvents: 0,\n      returnEvents: 0,\n      idleTime: 0\n    }\n  });\n\n  // Enhanced tracking state\n  const [performanceMetrics, setPerformanceMetrics] = useState({\n    frameRate: {\n      average: 0,\n      min: 0,\n      max: 0,\n      drops: 0\n    },\n    memoryUsage: {\n      used: 0,\n      total: 0\n    },\n    networkMetrics: {},\n    renderMetrics: {},\n    resourceLoadTimes: []\n  });\n  const [qualityMetrics, setQualityMetrics] = useState({\n    handDetectionAccuracy: 0,\n    backgroundRemovalQuality: 0,\n    productFitAccuracy: 0,\n    userSatisfactionScore: 0,\n    technicalIssues: []\n  });\n  const [conversionFunnel, setConversionFunnel] = useState({\n    viewedProduct: true,\n    initiatedTryOn: false,\n    completedTryOn: false,\n    sharedResult: false,\n    addedToCart: false,\n    proceededToCheckout: false,\n    completedPurchase: false\n  });\n\n  // Watch size customization state\n  const [userGender, setUserGender] = useState('men'); // Single gender role\n  const [userWristSize, setUserWristSize] = useState(50); // Default wrist size in mm\n  const [showWristSizeModal, setShowWristSizeModal] = useState(false); // Mobile-friendly modal\n\n  // Autocapture state variables\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [isHandInPosition, setIsHandInPosition] = useState(false);\n  const [isCountdownActive, setIsCountdownActive] = useState(false);\n\n  // Add new state for panel position\n  const [panelPosition, setPanelPosition] = useState(0);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startY, setStartY] = useState(0);\n  const panelRef = useRef(null);\n\n  // Realistic watch data with actual dimensions (in millimeters)\n  const watches = [{\n    name: \"Classic Black\",\n    path: \"imgs/watches/watch_1.png\",\n    // Rolex Submariner style - 40mm case\n    caseDiameter: 41,\n    // mm\n    caseThickness: 12.5,\n    // mm\n    totalWidth: 42,\n    // mm (including crown)\n    totalHeight: 47,\n    // mm (lug to lug)\n    dialDiameter: 31,\n    // mm (visible dial)\n    type: \"dress\",\n    dialSize: 40\n  }, {\n    name: \"Silver Chrono\",\n    path: \"watches/watch_2.png\",\n    // Omega Speedmaster style - 42mm case\n    caseDiameter: 42,\n    // mm\n    caseThickness: 13.2,\n    // mm\n    totalWidth: 44,\n    // mm\n    totalHeight: 48.5,\n    // mm\n    dialDiameter: 33,\n    // mm\n    type: \"sport\",\n    dialSize: 42\n  }, {\n    name: \"Gold Luxury\",\n    path: \"watches/watch_3.png\",\n    // Patek Philippe Calatrava style - 38mm case\n    caseDiameter: 39,\n    // mm\n    caseThickness: 8.5,\n    // mm\n    totalWidth: 39,\n    // mm\n    totalHeight: 45,\n    // mm\n    dialDiameter: 30,\n    // mm\n    type: \"luxury\",\n    dialSize: 38\n  }, {\n    name: \"Sport Blue\",\n    path: \"watches/watch_6.png\",\n    // Apple Watch style - 44mm case\n    caseDiameter: 41,\n    // mm (width)\n    caseThickness: 10.7,\n    // mm\n    totalWidth: 44,\n    // mm\n    totalHeight: 38,\n    // mm (height - rectangular)\n    dialDiameter: 35,\n    // mm (screen diagonal)\n    type: \"smartwatch\",\n    dialSize: 44\n  }, {\n    name: \"Minimalist\",\n    path: \"watches/watch_5.png\",\n    // Daniel Wellington style - 36mm case\n    caseDiameter: 36,\n    // mm\n    caseThickness: 6,\n    // mm\n    totalWidth: 37,\n    // mm\n    totalHeight: 43,\n    // mm\n    dialDiameter: 28,\n    // mm\n    type: \"minimalist\",\n    dialSize: 36\n  }, {\n    name: \"Rose Gold\",\n    path: \"watches/watch_4.png\",\n    // Michael Kors style - 39mm case\n    caseDiameter: 44,\n    // mm\n    caseThickness: 11,\n    // mm\n    totalWidth: 41,\n    // mm\n    totalHeight: 46,\n    // mm\n    dialDiameter: 31,\n    // mm\n    type: \"fashion\",\n    dialSize: 41\n  }];\n  const bracelets = [{\n    name: \"Silver Chain\",\n    path: \"bracelets/bracelet_1.png\"\n  }, {\n    name: \"Gold Bangle\",\n    path: \"bracelets/bracelet_2.png\"\n  }, {\n    name: \"Leather Wrap\",\n    path: \"bracelets/bracelet_3.png\"\n  }, {\n    name: \"Diamond Tennis\",\n    path: \"bracelets/bracelet_4.png\"\n  }, {\n    name: \"Beaded Stone\",\n    path: \"bracelets/bracelet_5.png\"\n  }, {\n    name: \"Charm Bracelet\",\n    path: \"bracelets/bracelet_6.png\"\n  }];\n\n  // Enhanced background removal function with proper bracelet processing sequence\n  const removeBackground = async (imgElement, productType = 'watch') => {\n    try {\n      let processedImageUrl = imgElement.src;\n\n      // For bracelets: Apply U-shape cutter → Background removal → Vertical flip\n      if (productType === 'bracelet') {\n        try {\n          // Step 1: Apply U-shape cutter first\n          processedImageUrl = await uShapeCutter(imgElement.src);\n          console.log('U-shape cutting applied successfully for bracelet');\n\n          // Step 2: Remove background after U-shape cutting\n          processedImageUrl = await removeProductBackground(processedImageUrl, productType);\n          console.log('Background removal applied after U-shape cutting');\n\n          // Step 3: Apply vertical flip based on hand detection (will be handled in transform)\n          imgElement.src = processedImageUrl;\n        } catch (error) {\n          console.warn('Bracelet processing sequence failed:', error);\n          // Fallback to basic processing\n          await basicBackgroundRemoval(imgElement, productType);\n        }\n      } else {\n        // For watches: Just apply background removal\n        const finalProcessedUrl = await removeProductBackground(processedImageUrl, productType);\n        imgElement.src = finalProcessedUrl;\n      }\n\n      // Apply product-specific styling that preserves all colors\n      imgElement.style.filter = 'none';\n      imgElement.style.mixBlendMode = 'normal';\n      imgElement.style.opacity = '1';\n      console.log(`Background removal completed for ${productType}`);\n    } catch (error) {\n      console.warn('Enhanced background removal failed:', error);\n      // Fallback to basic background removal\n      await basicBackgroundRemoval(imgElement, productType);\n    }\n  };\n\n  // Fallback basic background removal\n  const basicBackgroundRemoval = async (imgElement, productType) => {\n    try {\n      console.log(`Applying fallback background removal for ${productType}`);\n      if (productType === 'bracelet') {\n        // Apply U-shape cutter for bracelets\n        const uShapedImage = await uShapeCutter(imgElement.src);\n        imgElement.src = uShapedImage;\n      }\n\n      // Basic background removal with conservative settings\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.width = imgElement.naturalWidth;\n      canvas.height = imgElement.naturalHeight;\n      ctx.drawImage(imgElement, 0, 0);\n      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n      const data = imageData.data;\n\n      // Process each pixel - only remove very pure white backgrounds\n      for (let i = 0; i < data.length; i += 4) {\n        const r = data[i];\n        const g = data[i + 1];\n        const b = data[i + 2];\n\n        // Only remove very white pixels (more conservative for watches)\n        const threshold = productType === 'watch' ? 252 : 248;\n        if (r > threshold && g > threshold && b > threshold && Math.abs(r - g) < 3 && Math.abs(g - b) < 3) {\n          data[i + 3] = 0; // Make transparent\n        }\n      }\n      ctx.putImageData(imageData, 0, 0);\n      imgElement.src = canvas.toDataURL('image/png');\n      console.log(`Fallback background removal completed for ${productType}`);\n    } catch (error) {\n      console.error('Basic background removal failed:', error);\n    }\n  };\n\n  // Hand detection logic from tryon.js\n  const detectHandOrientation = imageData => {\n    // Simple heuristic for demo purposes - can be enhanced with ML models\n    return Math.random() > 0.5;\n  };\n\n  // Initialize camera\n  const initCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment',\n          width: {\n            ideal: 1920\n          },\n          height: {\n            ideal: 1080\n          }\n        }\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n    } catch (err) {\n      console.error(\"Error accessing camera:\", err);\n      // Demo mode fallback\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = \"sample-hand.jpg\";\n        capturedImageRef.current.style.display = \"block\";\n      }\n      console.log(\"Camera not available - demo mode\");\n      setIsCaptured(true);\n      setShowProductSelection(true);\n    }\n  };\n\n  // Capture current frame\n  const captureFrame = () => {\n    if (!videoRef.current || !canvasRef.current) return null;\n    const canvas = canvasRef.current;\n    const video = videoRef.current;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    const ctx = canvas.getContext('2d');\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n    return canvas.toDataURL('image/png');\n  };\n\n  // Parse URL parameters on component mount\n  useEffect(() => {\n    const parseUrlParams = () => {\n      const urlSearchParams = new URLSearchParams(window.location.search);\n      const params = {\n        image: urlSearchParams.get('image'),\n        client: urlSearchParams.get('client'),\n        size: urlSearchParams.get('size'),\n        type: urlSearchParams.get('type')\n      };\n      console.log('Parsed URL parameters:', params);\n      setUrlParams(params);\n\n      // If image URL is provided, set it as selected product and skip product selection\n      if (params.image) {\n        try {\n          // Validate the image URL\n          const imageUrl = new URL(params.image);\n          const productType = params.type || 'watches';\n          const defaultSize = productType === 'bracelets' ? 15 : 42;\n          const actualSize = parseInt(params.size) || defaultSize;\n          setSelectedProduct({\n            name: \"Custom Product\",\n            path: params.image,\n            caseDiameter: productType === 'watches' ? actualSize : null,\n            braceletWidth: productType === 'bracelets' ? actualSize : null,\n            caseThickness: productType === 'watches' ? 12 : null,\n            totalWidth: actualSize,\n            totalHeight: productType === 'watches' ? actualSize * 1.15 : actualSize * 3,\n            dialDiameter: productType === 'watches' ? actualSize * 0.75 : null,\n            type: productType,\n            clientId: params.client\n          });\n          setShowProductSelection(false);\n          console.log('Product loaded from URL:', {\n            image: params.image,\n            client: params.client,\n            size: params.size\n          });\n        } catch (error) {\n          console.error('Invalid image URL provided:', params.image);\n          // Show product selection if URL is invalid\n          setShowProductSelection(true);\n        }\n      }\n    };\n    parseUrlParams();\n  }, []);\n\n  // Start analytics session when URL parameters are loaded\n  useEffect(() => {\n    if (urlParams.client && urlParams.image) {\n      console.log('Starting analytics session with params:', urlParams);\n      startAnalyticsSession();\n\n      // Initialize enhanced tracking\n      const cleanup = initializeEnhancedTracking();\n\n      // Mark try-on as initiated\n      setConversionFunnel(prev => ({\n        ...prev,\n        initiatedTryOn: true\n      }));\n\n      // End session when user leaves the page\n      const handleBeforeUnload = () => {\n        endAnalyticsSession('abandoned');\n      };\n      const handleVisibilityChange = () => {\n        if (document.visibilityState === 'hidden') {\n          endAnalyticsSession('abandoned');\n        }\n      };\n      window.addEventListener('beforeunload', handleBeforeUnload);\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n      return () => {\n        cleanup();\n        window.removeEventListener('beforeunload', handleBeforeUnload);\n        document.removeEventListener('visibilitychange', handleVisibilityChange);\n        endAnalyticsSession('abandoned');\n      };\n    } else {\n      console.log('Missing required parameters for analytics:', {\n        hasClient: !!urlParams.client,\n        hasImage: !!urlParams.image\n      });\n    }\n  }, [urlParams]);\n\n  // Detect mobile device and set viewport height for Chrome mobile\n  useEffect(() => {\n    const checkDevice = () => {\n      const isMobileDevice = window.innerWidth <= 768;\n      setIsMobile(isMobileDevice);\n      setIsDesktop(!isMobileDevice);\n    };\n\n    // Fix viewport height for Chrome mobile\n    const setVH = () => {\n      const vh = window.innerHeight * 0.01;\n      document.documentElement.style.setProperty('--vh', `${vh}px`);\n    };\n    checkDevice();\n    setVH();\n    window.addEventListener('resize', () => {\n      checkDevice();\n      setVH();\n    });\n    window.addEventListener('orientationchange', () => {\n      setTimeout(() => {\n        setVH();\n      }, 100);\n    });\n    return () => {\n      window.removeEventListener('resize', checkDevice);\n      window.removeEventListener('orientationchange', setVH);\n    };\n  }, []);\n\n  // Initialize camera when component mounts\n  useEffect(() => {\n    if (typeof window !== 'undefined' && navigator.mediaDevices) {\n      initCamera();\n    }\n    // Show disclaimer popup when component first loads\n    setShowDisclaimerPopup(true);\n  }, []);\n\n  // Realistic sizing configuration\n  // Average adult wrist circumference: 165mm (men), 155mm (women)\n  // Average wrist width when viewed from top: ~55-65mm\n  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)\n  // This means the circle represents approximately 60mm in real life\n\n  // Universal wrist size configuration\n  const DEFAULT_WRIST_SIZE = 50; // mm - ideal wrist width from top view\n  const MIN_WRIST_SIZE = 35; // mm - minimum wrist width\n  const MAX_WRIST_SIZE = 65; // mm - maximum wrist width\n  const ASSUMED_DIAL_SIZE = 42; // mm - assumed real dial size for initial scaling\n\n  // Default wrist sizes by gender (top view width in mm)\n  const DEFAULT_WRIST_SIZES = {\n    men: 50 // mm - average men's wrist width from top view\n  };\n\n  // Add wrist size adjustment constant\n  const WRIST_SIZE_OFFSET = 10; // mm - subtract this from input wrist size for correct fitting\n  const MIN_ADJUSTED_WRIST_SIZE = 37; // Minimum adjusted wrist size before scaling stops\n\n  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units\n  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width\n  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height\n\n  // Size configuration - Increased for better visibility\n  const WATCH_WIDTH = 25; // percentage of container width\n  const BRACELET_WIDTH = 15; // percentage of container width\n  const WATCH_HEIGHT = 38; // percentage of container height\n  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility\n\n  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch\n  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch\n  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet\n  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet\n  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale\n\n  // Modify calculateWatchDimensions to use adjusted wrist size\n  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {\n    // Get the default wrist size for the current gender\n    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n\n    // Adjust the wrist size by subtracting the offset, but don't go below MIN_ADJUSTED_WRIST_SIZE\n    const adjustedWristSize = Math.max(userWristSize - WRIST_SIZE_OFFSET, MIN_ADJUSTED_WRIST_SIZE);\n\n    // Calculate INVERSE scaling factor - smaller wrist = larger watch, larger wrist = smaller watch\n    const inverseWristSizeRatio = defaultWristSize / adjustedWristSize;\n\n    // Calculate the scale factor from real world to SVG coordinates using adjusted wrist size\n    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / adjustedWristSize;\n\n    // Convert watch dimensions to SVG units\n    const watchWidthSvg = watch.totalWidth * mmToSvgScale;\n    const watchHeightSvg = watch.totalHeight * mmToSvgScale;\n    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;\n\n    // Convert SVG units to container percentages\n    const watchWidthPercent = watchWidthSvg / SVG_VIEWBOX_WIDTH * 100;\n    const watchHeightPercent = watchHeightSvg / SVG_VIEWBOX_HEIGHT * 100;\n    const dialDiameterPercent = dialDiameterSvg / SVG_VIEWBOX_WIDTH * 100;\n\n    // Calculate positioning - watches should be positioned on the wrist circle\n    const positionX = 50; // Center horizontally (400/800 = 50%)\n    const positionY = 50; // Center vertically (300/600 = 50%)\n\n    return {\n      width: Math.max(watchWidthPercent, 8),\n      // Minimum 8% for visibility\n      height: Math.max(watchHeightPercent, 10),\n      // Minimum 10% for visibility\n      dialDiameter: dialDiameterPercent,\n      positionX,\n      positionY,\n      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * inverseWristSizeRatio,\n      realWidth: watch.totalWidth,\n      realHeight: watch.totalHeight,\n      caseDiameter: watch.caseDiameter,\n      wristSizeRatio: inverseWristSizeRatio,\n      adjustedWristSize\n    };\n  };\n\n  // Calculate watch position based on hand orientation and anatomy\n  const getWatchPosition = (watchData, isRightHand) => {\n    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);\n\n    // Adjust position based on hand orientation\n    // Watches are typically worn on the top of the wrist\n    let adjustedX = baseDimensions.positionX;\n    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist\n\n    // For right hand, watch might be positioned slightly differently\n    if (isRightHand) {\n      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand\n    }\n\n    // Calculate scale to match SVG shape height\n    const svgHeight = 300; // Height of the wrist/forearm area in SVG\n    const watchHeight = watchData.totalHeight;\n    const scaleToFitHeight = svgHeight / watchHeight;\n\n    // Adjust for different watch types\n    switch (watchData.type) {\n      case 'smartwatch':\n        // Smart watches are often worn higher on the wrist\n        adjustedY -= 1;\n        break;\n      case 'luxury':\n        // Luxury watches might be positioned more precisely\n        adjustedY -= 0.5;\n        break;\n      case 'sport':\n        // Sport watches might be worn slightly looser\n        adjustedY += 0.5;\n        break;\n      default:\n        break;\n    }\n    return {\n      ...baseDimensions,\n      positionX: adjustedX,\n      positionY: adjustedY,\n      scale: Math.min(baseDimensions.scale, scaleToFitHeight) // Ensure watch doesn't exceed SVG height\n    };\n  };\n\n  // No static product data - products come from URL parameters or client integration\n\n  // Enhanced tracking functions\n  const initializeEnhancedTracking = () => {\n    // Mouse movement tracking\n    let mouseDistance = 0;\n    let lastMousePos = {\n      x: 0,\n      y: 0\n    };\n    const handleMouseMove = e => {\n      const distance = Math.sqrt(Math.pow(e.clientX - lastMousePos.x, 2) + Math.pow(e.clientY - lastMousePos.y, 2));\n      mouseDistance += distance;\n      lastMousePos = {\n        x: e.clientX,\n        y: e.clientY\n      };\n      setBehaviorMetrics(prev => ({\n        ...prev,\n        mouseMovements: mouseDistance\n      }));\n    };\n\n    // Scroll depth tracking\n    const handleScroll = () => {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      const docHeight = document.documentElement.scrollHeight - window.innerHeight;\n      const scrollPercent = Math.round(scrollTop / docHeight * 100);\n      setBehaviorMetrics(prev => ({\n        ...prev,\n        scrollDepth: Math.max(prev.scrollDepth, scrollPercent)\n      }));\n      trackInteraction('scroll', {\n        depth: scrollPercent,\n        position: scrollTop\n      });\n    };\n\n    // Focus/blur tracking for attention metrics\n    let focusStartTime = Date.now();\n    let isPageFocused = true;\n    const handleFocus = () => {\n      if (!isPageFocused) {\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          attentionMetrics: {\n            ...prev.attentionMetrics,\n            returnEvents: prev.attentionMetrics.returnEvents + 1\n          }\n        }));\n        focusStartTime = Date.now();\n        isPageFocused = true;\n      }\n    };\n    const handleBlur = () => {\n      if (isPageFocused) {\n        const focusTime = Date.now() - focusStartTime;\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          attentionMetrics: {\n            ...prev.attentionMetrics,\n            focusTime: prev.attentionMetrics.focusTime + focusTime,\n            blurEvents: prev.attentionMetrics.blurEvents + 1\n          }\n        }));\n        isPageFocused = false;\n      }\n    };\n\n    // Exit intent detection\n    const handleMouseLeave = e => {\n      if (e.clientY <= 0) {\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          exitIntent: {\n            detected: true,\n            timestamp: new Date(),\n            beforeConversion: !conversionFunnel.completedPurchase\n          }\n        }));\n        trackInteraction('exit_intent', {\n          timestamp: new Date()\n        });\n      }\n    };\n\n    // Performance monitoring\n    const monitorPerformance = () => {\n      if ('memory' in performance) {\n        const memory = performance.memory;\n        setPerformanceMetrics(prev => ({\n          ...prev,\n          memoryUsage: {\n            used: memory.usedJSHeapSize,\n            total: memory.totalJSHeapSize\n          }\n        }));\n      }\n\n      // Network information\n      if ('connection' in navigator) {\n        const connection = navigator.connection;\n        setPerformanceMetrics(prev => ({\n          ...prev,\n          networkMetrics: {\n            connectionType: connection.type,\n            downlink: connection.downlink,\n            rtt: connection.rtt,\n            effectiveType: connection.effectiveType\n          }\n        }));\n      }\n    };\n\n    // Add event listeners\n    document.addEventListener('mousemove', handleMouseMove);\n    window.addEventListener('scroll', handleScroll);\n    window.addEventListener('focus', handleFocus);\n    window.addEventListener('blur', handleBlur);\n    document.addEventListener('mouseleave', handleMouseLeave);\n\n    // Start performance monitoring\n    monitorPerformance();\n    const performanceInterval = setInterval(monitorPerformance, 5000);\n\n    // Cleanup function\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      window.removeEventListener('scroll', handleScroll);\n      window.removeEventListener('focus', handleFocus);\n      window.removeEventListener('blur', handleBlur);\n      document.removeEventListener('mouseleave', handleMouseLeave);\n      clearInterval(performanceInterval);\n    };\n  };\n\n  // Get user's IP address\n  const getUserIP = async () => {\n    try {\n      const response = await fetch('https://api.ipify.org?format=json');\n      const data = await response.json();\n      return data.ip;\n    } catch (error) {\n      console.warn('Could not get user IP:', error);\n      return null;\n    }\n  };\n\n  // Analytics tracking functions\n  const startAnalyticsSession = async () => {\n    if (!urlParams.client || !urlParams.image) {\n      console.warn('No client ID or image provided for analytics');\n      return;\n    }\n    try {\n      const pageLoadStartTime = performance.now();\n      const userIP = await getUserIP();\n      const sessionData = {\n        clientId: urlParams.client,\n        productId: urlParams.image,\n        productName: (selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.name) || 'Custom Product',\n        productCategory: urlParams.type || 'watches',\n        device: {\n          type: window.innerWidth <= 768 ? 'mobile' : window.innerWidth <= 1024 ? 'tablet' : 'desktop',\n          screenResolution: `${window.screen.width}x${window.screen.height}`,\n          os: navigator.platform,\n          browser: navigator.userAgent.split(' ').pop(),\n          userAgent: navigator.userAgent\n        },\n        location: {\n          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n          referrer: document.referrer,\n          userIP: userIP\n        },\n        behaviorMetrics: {\n          timeToFirstInteraction: null,\n          cameraInitSuccess: false,\n          handDetectionSuccess: false,\n          backgroundRemovalSuccess: false,\n          productSwitches: 0,\n          productViewTimes: [{\n            productId: urlParams.image,\n            duration: 0\n          }],\n          ...behaviorMetrics\n        },\n        performanceMetrics: {\n          pageLoadTime: null,\n          apiResponseTimes: [],\n          errors: [],\n          ...performanceMetrics\n        },\n        qualityMetrics,\n        conversionFunnel\n      };\n      console.log('Sending analytics session data:', sessionData);\n      const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n      const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session`;\n      const response = await fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(sessionData)\n      });\n      if (response.ok) {\n        const result = await response.json();\n        setSessionId(result.sessionId);\n        setSessionStartTime(new Date());\n\n        // Record page load time\n        const pageLoadTime = performance.now() - pageLoadStartTime;\n        trackPerformanceMetric('pageLoadTime', pageLoadTime);\n        console.log('Analytics session started:', result.sessionId);\n      } else {\n        const errorData = await response.json();\n        console.error('Failed to start analytics session:', errorData);\n        trackError('session_start', errorData.message);\n      }\n    } catch (error) {\n      console.error('Failed to start analytics session:', error);\n      trackError('session_start', error.message);\n    }\n  };\n  const trackInteraction = (type, data = {}) => {\n    const interaction = {\n      type,\n      timestamp: new Date(),\n      data\n    };\n\n    // Update behavior metrics based on interaction type\n    if (type === 'camera_init') {\n      updateBehaviorMetric('cameraInitSuccess', true);\n    } else if (type === 'hand_detection') {\n      updateBehaviorMetric('handDetectionSuccess', true);\n    } else if (type === 'background_removal') {\n      updateBehaviorMetric('backgroundRemovalSuccess', true);\n    } else if (type === 'product_switch') {\n      updateBehaviorMetric('productSwitches', prev => prev + 1);\n    }\n\n    // Enhanced interaction data\n    interaction.position = data.position || {\n      x: 0,\n      y: 0\n    };\n    interaction.element = data.element || '';\n    interaction.duration = data.duration || 0;\n    interaction.intensity = data.intensity || 0;\n    interaction.sequence = interactions.length + 1;\n    interaction.context = data.context || '';\n\n    // Update feature usage metrics using the new updateBehaviorMetric function\n    if (type === 'screenshot') {\n      updateBehaviorMetric('featureUsage.screenshot', prev => prev + 1);\n      setConversionFunnel(prev => ({\n        ...prev,\n        completedTryOn: true\n      }));\n    } else if (type === 'share') {\n      updateBehaviorMetric('featureUsage.share', prev => prev + 1);\n      setConversionFunnel(prev => ({\n        ...prev,\n        sharedResult: true\n      }));\n    } else if (type === 'zoom') {\n      updateBehaviorMetric('featureUsage.zoom', prev => prev + 1);\n    } else if (type === 'size_adjustment') {\n      updateBehaviorMetric('featureUsage.sizeAdjustment', prev => prev + 1);\n    } else if (type === 'color_change') {\n      updateBehaviorMetric('featureUsage.colorChange', prev => prev + 1);\n    } else if (type === 'product_switch') {\n      updateBehaviorMetric('featureUsage.productRotation', prev => prev + 1);\n    } else if (type === 'capture') {\n      updateBehaviorMetric('featureUsage.cameraToggle', prev => prev + 1);\n    }\n\n    // Track time to first interaction if not set\n    if (!behaviorMetrics.timeToFirstInteraction && sessionStartTime) {\n      const timeToFirstInteraction = (new Date() - sessionStartTime) / 1000;\n      updateBehaviorMetric('timeToFirstInteraction', timeToFirstInteraction);\n    }\n\n    // Calculate engagement score\n    const engagementScore = Math.min(100, interactions.length * 5 + behaviorMetrics.featureUsage.screenshot * 10 + behaviorMetrics.featureUsage.share * 15 + behaviorMetrics.scrollDepth * 0.2 + behaviorMetrics.mouseMovements * 0.001);\n    setBehaviorMetrics(prev => ({\n      ...prev,\n      engagementScore: Math.round(engagementScore)\n    }));\n    setInteractions(prev => [...prev, interaction]);\n    console.log('Enhanced interaction tracked:', interaction);\n  };\n  const trackPerformanceMetric = (metric, value) => {\n    const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n    const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}/performance`;\n    fetch(apiUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        metric,\n        value,\n        timestamp: new Date()\n      })\n    }).catch(error => {\n      console.error('Failed to track performance metric:', error);\n    });\n  };\n  const trackError = (type, message) => {\n    const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n    const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}/error`;\n    fetch(apiUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        type,\n        message,\n        timestamp: new Date()\n      })\n    }).catch(error => {\n      console.error('Failed to track error:', error);\n    });\n  };\n  const updateBehaviorMetric = (metric, value) => {\n    // Update local state\n    setBehaviorMetrics(prev => {\n      const newMetrics = {\n        ...prev\n      };\n\n      // Handle nested properties like 'featureUsage.screenshot'\n      if (metric.includes('.')) {\n        const [parent, child] = metric.split('.');\n        if (newMetrics[parent]) {\n          newMetrics[parent] = {\n            ...newMetrics[parent],\n            [child]: value\n          };\n        }\n      } else {\n        newMetrics[metric] = typeof value === 'function' ? value(prev[metric]) : value;\n      }\n      return newMetrics;\n    });\n\n    // Send to backend if session exists\n    if (sessionId) {\n      const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n      const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}/behavior`;\n      fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          metric,\n          value: typeof value === 'function' ? value(behaviorMetrics[metric] || 0) : value,\n          timestamp: new Date()\n        })\n      }).catch(error => {\n        console.error('Failed to update behavior metric:', error);\n      });\n    }\n  };\n  const endAnalyticsSession = async (outcome = 'abandoned') => {\n    if (!sessionId) return;\n    try {\n      const endTime = new Date();\n      const duration = sessionStartTime ? Math.floor((endTime - sessionStartTime) / 1000) : 0;\n      const updateData = {\n        endTime,\n        duration,\n        outcome,\n        interactions,\n        behaviorMetrics,\n        performanceMetrics,\n        qualityMetrics,\n        conversionFunnel\n      };\n      const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n      const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}`;\n      const response = await fetch(apiUrl, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(updateData)\n      });\n      if (response.ok) {\n        console.log('Analytics session ended:', outcome, 'Duration:', duration, 'seconds');\n      } else {\n        const errorData = await response.json();\n        console.error('Failed to end analytics session:', errorData);\n      }\n    } catch (error) {\n      console.error('Failed to end analytics session:', error);\n    }\n  };\n\n  // Detect if arm and wrist are within the SVG guide area\n  const detectHandInPosition = () => {\n    if (!videoRef.current || !canvasRef.current) return false;\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw current video frame\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Get the video container dimensions to calculate the guide area\n    const videoContainer = video.parentElement;\n    const containerRect = videoContainer.getBoundingClientRect();\n\n    // Calculate the actual video display area (accounting for object-fit: cover)\n    const videoAspect = video.videoWidth / video.videoHeight;\n    const containerAspect = containerRect.width / containerRect.height;\n    let displayWidth, displayHeight, offsetX, offsetY;\n    if (videoAspect > containerAspect) {\n      // Video is wider - height fills container, width is cropped\n      displayHeight = containerRect.height;\n      displayWidth = displayHeight * videoAspect;\n      offsetX = (displayWidth - containerRect.width) / 2;\n      offsetY = 0;\n    } else {\n      // Video is taller - width fills container, height is cropped\n      displayWidth = containerRect.width;\n      displayHeight = displayWidth / videoAspect;\n      offsetX = 0;\n      offsetY = (displayHeight - containerRect.height) / 2;\n    }\n\n    // Calculate the guide areas in canvas coordinates\n    // SVG viewBox is 800x600\n    // Main rectangle: x=\"320\" y=\"150\" width=\"160\" height=\"300\" (wrist/forearm area)\n    // Circle: cx=\"400\" cy=\"300\" r=\"60\" (hand area)\n\n    const scaleX = canvas.width / displayWidth;\n    const scaleY = canvas.height / displayHeight;\n\n    // Wrist/forearm area (rectangle)\n    const rectX = Math.max(0, (320 / 800 * displayWidth - offsetX) * scaleX);\n    const rectY = Math.max(0, (150 / 600 * displayHeight - offsetY) * scaleY);\n    const rectWidth = Math.min(canvas.width - rectX, 160 / 800 * displayWidth * scaleX);\n    const rectHeight = Math.min(canvas.height - rectY, 300 / 600 * displayHeight * scaleY);\n\n    // Hand area (circle)\n    const circleX = Math.max(0, (340 / 800 * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds\n    const circleY = Math.max(0, (240 / 600 * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds\n    const circleWidth = Math.min(canvas.width - circleX, 120 / 800 * displayWidth * scaleX); // Circle diameter\n    const circleHeight = Math.min(canvas.height - circleY, 120 / 600 * displayHeight * scaleY); // Circle diameter\n\n    try {\n      // Check wrist/forearm area (rectangle)\n      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);\n      const rectData = rectImageData.data;\n\n      // Check hand area (circle approximation)\n      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);\n      const circleData = circleImageData.data;\n      let rectSkinPixels = 0;\n      let rectTotalPixels = 0;\n      let circleSkinPixels = 0;\n      let circleTotalPixels = 0;\n\n      // Enhanced skin tone detection\n      const isSkinTone = (r, g, b) => {\n        // Multiple skin tone ranges to cover different skin colors\n        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;\n        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;\n        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;\n        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;\n        return condition1 || condition2 || condition3 || condition4;\n      };\n\n      // Analyze rectangle area (wrist/forearm)\n      for (let i = 0; i < rectData.length; i += 4) {\n        const r = rectData[i];\n        const g = rectData[i + 1];\n        const b = rectData[i + 2];\n        if (isSkinTone(r, g, b)) {\n          rectSkinPixels++;\n        }\n        rectTotalPixels++;\n      }\n\n      // Analyze circle area (hand)\n      for (let i = 0; i < circleData.length; i += 4) {\n        const r = circleData[i];\n        const g = circleData[i + 1];\n        const b = circleData[i + 2];\n        if (isSkinTone(r, g, b)) {\n          circleSkinPixels++;\n        }\n        circleTotalPixels++;\n      }\n\n      // Calculate skin ratios\n      const rectSkinRatio = rectSkinPixels / rectTotalPixels;\n      const circleSkinRatio = circleSkinPixels / circleTotalPixels;\n\n      // Both wrist/forearm AND hand areas must have sufficient skin tone presence\n      // Wrist area needs at least 20% skin tone (forearm/wrist)\n      // Hand area needs at least 25% skin tone (hand/fingers)\n      const wristInPosition = rectSkinRatio > 0.20;\n      const handInPosition = circleSkinRatio > 0.25;\n      return wristInPosition && handInPosition;\n    } catch (error) {\n      console.warn('Hand detection error:', error);\n      return false;\n    }\n  };\n\n  // Apply product to watch position with dynamic scaling\n  const applyProductToWatchPosition = (productPath, productType) => {\n    // Clear any existing product first\n    setSelectedProduct(null);\n\n    // Find the watch data for dial size\n    const watchData = watches.find(w => w.path === productPath);\n\n    // Small delay to ensure the old product is removed before adding new one\n    setTimeout(() => {\n      setSelectedProduct({\n        path: productPath,\n        dialSize: (watchData === null || watchData === void 0 ? void 0 : watchData.dialSize) || 40,\n        // Default to 40mm if not found\n        dimensions: watchData // Pass full watch dimensions for scaling\n      });\n    }, 50);\n  };\n\n  // Screenshot functionality with tracking\n  const takeScreenshot = () => {\n    if (!capturedImageRef.current) return;\n    try {\n      // Create a canvas to combine the captured image with the product overlay\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n\n      // Set canvas size\n      canvas.width = capturedImageRef.current.naturalWidth || 800;\n      canvas.height = capturedImageRef.current.naturalHeight || 600;\n\n      // Draw the captured image\n      ctx.drawImage(capturedImageRef.current, 0, 0, canvas.width, canvas.height);\n\n      // Convert to blob and download\n      canvas.toBlob(blob => {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `tryon-${Date.now()}.png`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n\n        // Track screenshot\n        trackInteraction('screenshot', {\n          timestamp: new Date(),\n          productId: urlParams.image,\n          sessionDuration: sessionStartTime ? (new Date() - sessionStartTime) / 1000 : 0\n        });\n\n        // Mark as completed try-on\n        setConversionFunnel(prev => ({\n          ...prev,\n          completedTryOn: true\n        }));\n      }, 'image/png');\n    } catch (error) {\n      console.error('Screenshot failed:', error);\n      trackError('screenshot_failed', error.message);\n    }\n  };\n\n  // Zoom functionality with tracking\n  const handleZoom = zoomLevel => {\n    trackInteraction('zoom', {\n      zoomLevel,\n      timestamp: new Date(),\n      element: 'product_overlay'\n    });\n  };\n\n  // Share functionality with tracking\n  const handleShare = async () => {\n    try {\n      if (navigator.share) {\n        await navigator.share({\n          title: 'Check out my virtual try-on!',\n          text: 'I tried on this product virtually',\n          url: window.location.href\n        });\n        trackInteraction('share', {\n          method: 'native_share',\n          timestamp: new Date()\n        });\n        setConversionFunnel(prev => ({\n          ...prev,\n          sharedResult: true\n        }));\n      } else {\n        // Fallback to copying URL\n        await navigator.clipboard.writeText(window.location.href);\n        alert('Link copied to clipboard!');\n        trackInteraction('share', {\n          method: 'copy_link',\n          timestamp: new Date()\n        });\n      }\n    } catch (error) {\n      console.error('Share failed:', error);\n      trackError('share_failed', error.message);\n    }\n  };\n\n  // Handle Try On Model button click\n  const handleTryOnModel = () => {\n    if (!isCaptured) {\n      // Load the model hand image from public folder\n      const modelImagePath = '/imgs/hand/hand.png'; // Using the hand.jpg from public folder\n\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = modelImagePath;\n        capturedImageRef.current.style.display = 'block';\n\n        // Wait for image to load, then detect wrist and apply try-on logic\n        capturedImageRef.current.onload = () => {\n          // Simulate wrist detection on the model image\n          detectWristOnModelImage();\n        };\n      }\n      setIsCaptured(true);\n      setIsUsingModelImage(true);\n      // Only show product selection if no product is provided via URL\n      if (!urlParams.image) {\n        setShowProductSelection(true);\n      }\n      setShowHandGuide(false);\n\n      // Track try-on model interaction\n      trackInteraction('capture', {\n        method: 'model_tryon',\n        hasProduct: !!urlParams.image,\n        timestamp: new Date()\n      });\n\n      // Set hand orientation for model (assume left hand for consistency)\n      setIsRightHand(false);\n    }\n  };\n\n  // Detect wrist position on model image and apply scaling\n  const detectWristOnModelImage = () => {\n    if (!capturedImageRef.current) return;\n    try {\n      // Create a canvas to analyze the model image\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = capturedImageRef.current;\n      canvas.width = img.naturalWidth || 800;\n      canvas.height = img.naturalHeight || 600;\n      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n\n      // For model image with closed palm, position the watch/bracelet on the palm area\n      // Since it's a closed hand, we need to detect the palm center rather than wrist\n      const palmPosition = detectPalmPositionOnModel(canvas);\n      if (palmPosition) {\n        console.log('Palm position detected on model image:', palmPosition);\n\n        // Store the palm position for product placement\n        window.modelPalmPosition = palmPosition;\n\n        // Mark behavior metrics for successful hand detection\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          handDetectionSuccess: true,\n          palmDetected: true\n        }));\n\n        // Track successful palm detection\n        trackInteraction('hand_detection', {\n          method: 'model_image_palm',\n          success: true,\n          palmPosition: palmPosition,\n          timestamp: new Date()\n        });\n      } else {\n        console.warn('Could not detect palm position on model image');\n      }\n    } catch (error) {\n      console.warn('Model image palm detection error:', error);\n      trackError('model_palm_detection', error.message);\n    }\n  };\n\n  // Detect palm position on closed hand model image\n  const detectPalmPositionOnModel = canvas => {\n    try {\n      const ctx = canvas.getContext('2d');\n      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n      const data = imageData.data;\n\n      // For a closed hand model, we need to find the wrist area where the watch/bracelet should go\n      // The wrist is typically at the bottom of the hand, where the arm connects\n\n      // Scan from bottom up to find the wrist area (narrowest part of the arm)\n      let wristY = null;\n      let wristX = canvas.width * 0.5; // Start from center\n      let minWidth = canvas.width;\n\n      // Scan the bottom third of the image to find the wrist\n      const startY = Math.floor(canvas.height * 0.7); // Start from 70% down\n      const endY = canvas.height - 20; // Stop 20px from bottom\n\n      for (let y = startY; y < endY; y += 2) {\n        let leftEdge = null;\n        let rightEdge = null;\n\n        // Scan horizontally to find the edges of the arm/wrist\n        for (let x = 0; x < canvas.width; x++) {\n          const pixelIndex = (y * canvas.width + x) * 4;\n          const r = data[pixelIndex];\n          const g = data[pixelIndex + 1];\n          const b = data[pixelIndex + 2];\n          const alpha = data[pixelIndex + 3];\n\n          // Check if pixel is part of the hand/arm (not background)\n          // Adjust threshold based on the hand image characteristics\n          const isHandPixel = alpha > 100 && (r < 200 || g < 200 || b < 200);\n          if (isHandPixel && leftEdge === null) {\n            leftEdge = x;\n          }\n          if (isHandPixel) {\n            rightEdge = x;\n          }\n        }\n\n        // If we found both edges, check if this is the narrowest point (wrist)\n        if (leftEdge !== null && rightEdge !== null) {\n          const armWidth = rightEdge - leftEdge;\n          if (armWidth < minWidth && armWidth > 40) {\n            // Reasonable wrist width\n            minWidth = armWidth;\n            wristY = y;\n            wristX = leftEdge + armWidth / 2;\n          }\n        }\n      }\n\n      // If we couldn't detect the wrist automatically, use default position\n      if (!wristY) {\n        wristY = canvas.height * 0.65; // 85% down from top (moved down a bit)\n        wristX = canvas.width * 0.5; // Center horizontally\n        console.log('Using default wrist position for closed hand model');\n      }\n\n      // Calculate the wrist area for product placement\n      // Make the watch bigger for model hand since it's larger than SVG\n      const wristBounds = {\n        x: wristX - 80,\n        // Increased wrist area width for bigger watch\n        y: wristY - 40,\n        // Increased wrist area height for bigger watch\n        width: 160,\n        // Increased from 120 to make watch bigger\n        height: 60,\n        // Increased from 60 to make watch bigger\n        centerX: wristX,\n        centerY: wristY + 10,\n        // Move center down a bit\n        confidence: wristY ? 0.9 : 0.7,\n        isModelHand: true // Flag to indicate this is model hand for scaling adjustments\n      };\n      console.log('Detected wrist area for closed hand model:', wristBounds);\n      return wristBounds;\n    } catch (error) {\n      console.error('Error detecting wrist position on closed hand:', error);\n      // Return default wrist position with bigger size for model hand\n      return {\n        x: canvas.width * 0.5 - 80,\n        y: canvas.height * 0.85 - 40,\n        width: 160,\n        height: 80,\n        centerX: canvas.width * 0.5,\n        centerY: canvas.height * 0.85 + 10,\n        confidence: 0.7,\n        isModelHand: true\n      };\n    }\n  };\n\n  // Handle capture button click\n  const handleCapture = () => {\n    if (!isCaptured) {\n      const capturedDataUrl = captureFrame();\n      if (capturedImageRef.current && capturedDataUrl) {\n        capturedImageRef.current.src = capturedDataUrl;\n        capturedImageRef.current.style.display = 'block';\n      }\n      setIsCaptured(true);\n      setIsUsingModelImage(false);\n      // Only show product selection if no product is provided via URL\n      if (!urlParams.image) {\n        setShowProductSelection(true);\n      }\n      setShowHandGuide(false);\n\n      // Track capture interaction\n      trackInteraction('capture', {\n        method: 'manual_capture',\n        hasProduct: !!urlParams.image,\n        timestamp: new Date()\n      });\n\n      // Detect hand orientation\n      if (canvasRef.current && videoRef.current) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        canvas.width = videoRef.current.videoWidth;\n        canvas.height = videoRef.current.videoHeight;\n        ctx.drawImage(videoRef.current, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        setIsRightHand(detectHandOrientation(imageData));\n      }\n    } else {\n      // Only show product selection if no product is provided via URL\n      if (!urlParams.image) {\n        setShowProductSelection(true);\n      }\n    }\n  };\n\n  // Handle back button click\n  const handleBack = () => {\n    if (capturedImageRef.current) {\n      capturedImageRef.current.style.display = 'none';\n    }\n    setIsCaptured(false);\n    setSelectedProduct(null);\n    setIsRightHand(false);\n    setShowProductSelection(false);\n    setShowWristSizeModal(false);\n    setShowHandGuide(true);\n    setIsUsingModelImage(false); // Reset model image state\n  };\n\n  // Handle gender selection\n  const handleGenderChange = gender => {\n    setUserGender('men'); // Single gender role\n    setUserWristSize(50); // Set initial size for men\n    trackInteraction('size_adjustment', {\n      type: 'gender_change',\n      gender: 'men',\n      newWristSize: 50\n    });\n  };\n\n  // Handle wrist size change\n  const handleWristSizeChange = size => {\n    setUserWristSize(size);\n    trackInteraction('size_adjustment', {\n      type: 'wrist_size_change',\n      size\n    });\n  };\n\n  // Handle tab change\n  const handleTabChange = tabName => {\n    setActiveTab(tabName);\n  };\n\n  // Handle autocapture switch toggle\n  const handleAutoCaptureToggle = () => {\n    const newState = !isAutoCaptureEnabled;\n    setIsAutoCaptureEnabled(newState);\n\n    // Reset states when turning off\n    if (!newState) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      setIsHandInPosition(false);\n    }\n  };\n\n  // Reset autocapture when going back\n  const handleBackWithReset = () => {\n    setIsAutoCaptureEnabled(false);\n    setIsCountdownActive(false);\n    setCountdown(0);\n    setIsHandInPosition(false);\n    setShowWristSizeModal(false);\n    setUserWristSize(50); // Default to men's size\n    setIsUsingModelImage(false); // Reset model image state\n    // Go back to wherever user came from instead of just resetting\n    onBackToHome();\n  };\n\n  // Get current products based on active tab\n  const getCurrentProducts = () => {\n    return activeTab === 'Watches' ? watches : bracelets;\n  };\n\n  // Handle product selection\n  const handleProductSelect = product => {\n    applyProductToWatchPosition(product.path, activeTab);\n  };\n\n  // Handle disclaimer popup close\n  const handleDisclaimerClose = () => {\n    setShowDisclaimerPopup(false);\n  };\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Add touch gesture handlers\n  const handleTouchStart = e => {\n    setIsDragging(true);\n    setStartY(e.touches[0].clientY);\n  };\n  const handleTouchMove = e => {\n    if (!isDragging) return;\n    const currentY = e.touches[0].clientY;\n    const diff = currentY - startY;\n\n    // Only allow dragging down\n    if (diff > 0) {\n      setPanelPosition(diff);\n    }\n  };\n  const handleTouchEnd = () => {\n    if (!isDragging) return;\n    setIsDragging(false);\n\n    // If dragged more than 100px down, close the panel\n    if (panelPosition > 100) {\n      setShowProductSelection(false);\n    }\n\n    // Reset position\n    setPanelPosition(0);\n  };\n\n  // Add click outside handler for modals\n  useEffect(() => {\n    const handleClickOutside = e => {\n      if (showWristSizeModal && !e.target.closest('.modal-content')) {\n        setShowWristSizeModal(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    document.addEventListener('touchstart', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('touchstart', handleClickOutside);\n    };\n  }, [showWristSizeModal]);\n\n  // Desktop QR Code Component\n  const DesktopQRCode = () => {\n    // Generate dynamic QR code URL based on URL parameters or default\n    const generateQRValue = () => {\n      const websiteUrl = process.env.REACT_APP_WEBSITE_URL || 'https://viatryon.com';\n      const baseUrl = `${websiteUrl}/tryon`;\n\n      // If we have URL parameters, include them in the QR code\n      if (urlParams.image || urlParams.client || urlParams.size || urlParams.type) {\n        const params = new URLSearchParams();\n        if (urlParams.image) params.append('image', urlParams.image);\n        if (urlParams.client) params.append('client', urlParams.client);\n        if (urlParams.size) params.append('size', urlParams.size);\n        if (urlParams.type) params.append('type', urlParams.type);\n        return `${baseUrl}?${params.toString()}`;\n      }\n\n      // Default QR code for general try-on\n      return baseUrl;\n    };\n    const qrValue = generateQRValue();\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.desktopContainer,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.qrContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: styles.qrTitle,\n          children: \"Scan QR Code to Try On\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1873,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: styles.qrSubtitle,\n          children: urlParams.image ? \"Scan to try on this specific product on your mobile device\" : \"Open this page on your mobile device to experience the virtual try-on feature\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1874,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.qrWrapper,\n          children: /*#__PURE__*/_jsxDEV(QRCodeSVG, {\n            value: qrValue,\n            size: 256,\n            level: \"H\",\n            bgColor: \"#FFFFFF\",\n            fgColor: \"#2D8C88\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1881,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1880,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: styles.qrLink,\n          children: qrValue\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1889,\n          columnNumber: 11\n        }, this), urlParams.client && /*#__PURE__*/_jsxDEV(\"p\", {\n          style: styles.clientInfo,\n          children: [\"Client: \", urlParams.client]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1891,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.homeBtn,\n          onClick: onBackToHome,\n          \"aria-label\": \"Home\",\n          children: \"\\u2190 Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1893,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1872,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1871,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Return desktop view if not on mobile\n  if (isDesktop) {\n    return /*#__PURE__*/_jsxDEV(DesktopQRCode, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1907,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Update product selection panel JSX\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: [showDisclaimerPopup && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.disclaimerOverlay,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.disclaimerPopup,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.disclaimerContent,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: styles.disclaimerTitle,\n            children: \"Before You Start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1918,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.disclaimerPoints,\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              style: styles.disclaimerPoint,\n              children: \"Images shown are for demonstration purposes - actual size may vary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1920,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: styles.disclaimerPoint,\n              children: \"Position your wrist within the guide lines for better results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1923,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1919,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.disclaimerButton,\n            onClick: handleDisclaimerClose,\n            children: \"Got it\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1927,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1917,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1916,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1915,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.brandingContainerTangiblee,\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/imgs/logo-only.png\",\n        alt: \"ViaTryon\",\n        style: styles.brandingLogoTangiblee,\n        onError: e => {\n          e.target.style.display = 'none';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1940,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.brandingTextStacked,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            ...styles.poweredByText,\n            color: isUsingModelImage ? '#000000' : styles.poweredByText.color\n          },\n          children: \"Powered by\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1949,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            ...styles.viatryonText,\n            color: isUsingModelImage ? '#000000' : styles.viatryonText.color\n          },\n          children: \"ViaTryon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1953,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1948,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1939,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.cameraContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"video\", {\n        ref: videoRef,\n        style: styles.cameraFeed,\n        autoPlay: true,\n        playsInline: true,\n        muted: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1961,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1968,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        ref: capturedImageRef,\n        style: styles.capturedImage,\n        alt: \"Captured hand\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1969,\n        columnNumber: 9\n      }, this), !isCaptured && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.autoCaptureToggle,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: styles.autoCaptureLabel,\n          children: \"Auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1978,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"switch-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: isAutoCaptureEnabled,\n            onChange: handleAutoCaptureToggle,\n            disabled: isCountdownActive,\n            \"aria-label\": \"Toggle auto capture\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1980,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"switch-slider\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1987,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1979,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1977,\n        columnNumber: 11\n      }, this), isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.countdownDisplay,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.countdownNumber,\n          children: countdown\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1995,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.countdownText,\n          children: \"Auto capturing...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1996,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1994,\n        columnNumber: 11\n      }, this), !isCaptured && !isCountdownActive && !isAutoCaptureEnabled && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.instructionContainer,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.instructionText,\n          children: \"Position your wrist within the guides\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2003,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2002,\n        columnNumber: 11\n      }, this), isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.statusMessageSmall,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.statusTextSmall,\n          children: \"Position your arm and wrist in the guide area\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2012,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.statusSubtextSmall,\n          children: \"Countdown will start automatically when detected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2013,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2011,\n        columnNumber: 11\n      }, this), isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.statusMessage,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.statusText,\n            backgroundColor: 'rgba(45, 140, 136, 0.9)'\n          },\n          children: \"Perfect! Starting countdown...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2019,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2018,\n        columnNumber: 11\n      }, this), showHandGuide && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.handGuide,\n          opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,\n          filter: isAutoCaptureEnabled && isHandInPosition ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))' : isAutoCaptureEnabled && !isHandInPosition ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))' : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'\n        },\n        className: isMobile ? 'mobile-hand-guide' : '',\n        \"aria-hidden\": \"true\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          viewBox: \"0 0 800 600\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"5\",\n            fill: \"none\",\n            strokeLinecap: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2042,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"5\",\n            fill: \"none\",\n            strokeLinecap: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2055,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n            x: \"320\",\n            y: \"150\",\n            width: \"160\",\n            height: \"300\",\n            fill: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            opacity: isAutoCaptureEnabled && isHandInPosition ? \"0.25\" : \"0.15\",\n            rx: \"15\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2069,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"400\",\n            cy: \"300\",\n            r: \"60\",\n            fill: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            opacity: isAutoCaptureEnabled && isHandInPosition ? \"0.3\" : \"0.2\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2093,\n            columnNumber: 15\n          }, this), isAutoCaptureEnabled && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"140\",\n              textAnchor: \"middle\",\n              fill: \"white\",\n              fontSize: \"14\",\n              fontWeight: \"bold\",\n              children: \"WRIST & FOREARM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2117,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"480\",\n              textAnchor: \"middle\",\n              fill: \"white\",\n              fontSize: \"14\",\n              fontWeight: \"bold\",\n              children: \"HAND\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2120,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2040,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2027,\n        columnNumber: 11\n      }, this), selectedProduct && isCaptured && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.productPosition,\n          // Adjust positioning for model hand - move down and make bigger\n          top: isUsingModelImage ? '55%' : '50%',\n          transform: isUsingModelImage ? 'translate(-50%, -50%) scale(1.3)' : 'translate(-50%, -50%)',\n          width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\n          height: activeTab === 'Watches' ? (() => {\n            const defaultWristSize = DEFAULT_WRIST_SIZE;\n            const isLargeWrist = userWristSize >= DEFAULT_WRIST_SIZE;\n            if (isLargeWrist) {\n              // For large wrists, increase height by 40% to allow exceeding SVG height\n              const sizeIncrease = (userWristSize - DEFAULT_WRIST_SIZE) / DEFAULT_WRIST_SIZE;\n              return `${WATCH_HEIGHT * (1 + sizeIncrease * 0.9)}%`;\n            }\n            return `${WATCH_HEIGHT}%`;\n          })() : `${BRACELET_HEIGHT}%`,\n          // Apply clipping for wrist sizes >= 50mm (men) and >= 45mm (women)\n          clipPath: (() => {\n            const isLargeWrist = userWristSize >= DEFAULT_WRIST_SIZE;\n            return activeTab === 'Watches' && isLargeWrist ? 'ellipse(220px 60px at 50% 50%)' : activeTab === 'Watches' && userWristSize < DEFAULT_WRIST_SIZE ? 'ellipse(220px 60px at 50% 50%)' : 'none';\n          })(),\n          overflow: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct,\n            alt: \"Selected product\",\n            style: {\n              width: '100%',\n              height: '100%',\n              objectFit: 'contain',\n              transform: activeTab === 'Bracelets' ? (() => {\n                // Use exact bracelet fitting logic from tryon.js with enhanced vertical flip\n                const baseTransform = `rotate(90deg) scale(${BRACELET_HEIGHT / 30})`;\n\n                // Apply realistic bracelet positioning based on hand detection\n                // Bracelets need vertical flipping to appear correctly on the wrist\n                if (isRightHand) {\n                  // For right hand: flip horizontally (like tryon.js) and add vertical flip for realism\n                  return `${baseTransform} scaleX(-1) scaleY(-1)`;\n                } else {\n                  // For left hand: only vertical flip for proper bracelet orientation\n                  return `${baseTransform} scaleY(-1)`;\n                }\n              })() : (_selectedProduct$dime => {\n                const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n                const adjustedWristSize = Math.max(userWristSize - WRIST_SIZE_OFFSET, MIN_ADJUSTED_WRIST_SIZE);\n                const isLargeWrist = userGender === 'men' && adjustedWristSize >= 50;\n                if (isLargeWrist) {\n                  // For larger wrists, apply height scaling increase and allow exceeding SVG height\n                  const sizeIncrease = (adjustedWristSize - defaultWristSize) / defaultWristSize;\n                  const heightScale = 1 + sizeIncrease * 0.4; // 40% height increase\n                  const widthScale = defaultWristSize / adjustedWristSize; // Decrease width as wrist increases\n\n                  return `scale(${WATCH_HEIGHT / 25 * widthScale}) scaleX(${widthScale}) scaleY(${heightScale})`;\n                }\n\n                // For smaller wrists, use the original working logic with SVG height constraint\n                return `scale(${Math.min(WATCH_HEIGHT / 25 * (adjustedWristSize > defaultWristSize ? defaultWristSize / adjustedWristSize : defaultWristSize / adjustedWristSize), 300 / (((_selectedProduct$dime = selectedProduct.dimensions) === null || _selectedProduct$dime === void 0 ? void 0 : _selectedProduct$dime.totalHeight) || 47) // Scale to match SVG height\n                )}) scaleX(${adjustedWristSize > defaultWristSize ? defaultWristSize / adjustedWristSize : 1})`;\n              })(),\n              filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\n            },\n            onLoad: e => removeBackground(e.target, urlParams.type === 'bracelets' || activeTab === 'Bracelets' ? 'bracelet' : 'watch')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2169,\n            columnNumber: 15\n          }, this), (urlParams.type !== 'bracelets' && activeTab === 'Watches' || !urlParams.type && activeTab === 'Watches') && typeof selectedProduct === 'object' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              bottom: '-30px',\n              left: '50%',\n              transform: 'translateX(-50%)',\n              fontSize: '11px',\n              fontWeight: '600',\n              color: 'white',\n              backgroundColor: 'rgba(45, 140, 136, 0.9)',\n              padding: '3px 8px',\n              borderRadius: '12px',\n              whiteSpace: 'nowrap',\n              pointerEvents: 'none',\n              boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n              zIndex: 2\n            },\n            children: [selectedProduct.dialSize || selectedProduct.caseDiameter || urlParams.size || '42', \"mm\", userWristSize !== DEFAULT_WRIST_SIZE && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '10px',\n                opacity: 0.8,\n                marginLeft: '4px'\n              },\n              children: (() => {\n                const wristSizeRatio = DEFAULT_WRIST_SIZE / userWristSize;\n                let scalingPercentage;\n                if (userWristSize < DEFAULT_WRIST_SIZE) {\n                  // Realistic scaling for smaller wrists\n                  const sizeDifference = DEFAULT_WRIST_SIZE - userWristSize;\n                  const maxSizeDifference = DEFAULT_WRIST_SIZE * 0.25;\n                  const clampedDifference = Math.min(sizeDifference, maxSizeDifference);\n                  const moderateScaleFactor = 1 + clampedDifference / DEFAULT_WRIST_SIZE * 0.6;\n                  scalingPercentage = ((moderateScaleFactor - 1) * 100).toFixed(0);\n                } else {\n                  // Standard scaling for larger wrists\n                  scalingPercentage = ((wristSizeRatio - 1) * 100).toFixed(0);\n                }\n\n                // Add debug indicator for large wrists\n                const debugSuffix = userWristSize >= DEFAULT_WRIST_SIZE ? ' 🔥' : '';\n                return `(${userWristSize < DEFAULT_WRIST_SIZE ? '+' : ''}${scalingPercentage}%)${debugSuffix}`;\n              })()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2238,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2220,\n            columnNumber: 17\n          }, this), (urlParams.type === 'bracelets' || activeTab === 'Bracelets') && typeof selectedProduct === 'object' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              bottom: '-30px',\n              left: '50%',\n              transform: 'translateX(-50%)',\n              fontSize: '11px',\n              fontWeight: '600',\n              color: 'white',\n              backgroundColor: 'rgba(45, 140, 136, 0.9)',\n              padding: '3px 8px',\n              borderRadius: '12px',\n              whiteSpace: 'nowrap',\n              pointerEvents: 'none',\n              boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n              zIndex: 2\n            },\n            children: [selectedProduct.braceletWidth || urlParams.size || '15', \"mm width\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2268,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2161,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2131,\n        columnNumber: 11\n      }, this), !isCaptured && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.captureButtonContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.captureButtonWrapper,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.captureBtn,\n            className: isMobile ? 'mobile-capture-btn' : '',\n            onClick: handleCapture,\n            \"aria-label\": \"Capture\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.captureInner,\n              className: isMobile ? 'mobile-inner-circle' : ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2301,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2295,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: styles.buttonLabel,\n            children: \"Capture\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2303,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.modelButtonWrapper,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.modelBtn,\n            className: isMobile ? 'mobile-model-btn' : '',\n            onClick: handleTryOnModel,\n            \"aria-label\": \"Try on model\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"white\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M19,13H16.5L15.5,15H14L16,11H13V7H11V11H8L10,15H8.5L7.5,13H5C4.46,13 4,13.46 4,14V16C4,16.54 4.46,17 5,17H6V19C6,20.11 6.9,21 8,21H16C17.11,21 18,20.11 18,19V17H19C19.54,17 20,16.54 20,16V14C20,13.46 19.54,13 19,13M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2315,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2314,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2308,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: styles.buttonLabel,\n            children: \"Try Model\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2318,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2307,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2293,\n        columnNumber: 11\n      }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.resetBtn,\n        onClick: () => window.location.reload(),\n        \"aria-label\": \"Reset\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"20\",\n          height: \"20\",\n          viewBox: \"0 0 24 24\",\n          fill: \"white\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2331,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2330,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2325,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1960,\n      columnNumber: 7\n    }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n      style: styles.wristSizeFloatingBtn,\n      className: isMobile ? 'mobile-btn' : '',\n      onClick: () => setShowWristSizeModal(true),\n      \"aria-label\": \"Adjust wrist size\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"white\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2346,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2345,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: styles.wristSizeText,\n        children: [userWristSize, \"mm\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2348,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2339,\n      columnNumber: 9\n    }, this), showWristSizeModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.modalOverlay,\n      onClick: () => setShowWristSizeModal(false),\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.wristSizeModal,\n        onClick: e => e.stopPropagation(),\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.modalHeader,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: styles.modalTitle,\n            children: \"Adjust Wrist Size\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2365,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.modalCloseBtn,\n            onClick: () => setShowWristSizeModal(false),\n            \"aria-label\": \"Close\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2372,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2371,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2366,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2364,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.modalContent,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.genderSelection,\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...styles.genderButton,\n                ...styles.genderButtonActive\n              },\n              onClick: () => handleGenderChange('men'),\n              children: \"Standard (50mm)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2380,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2379,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.sliderContainer,\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: styles.sliderLabel,\n              children: [\"Wrist Size: \", userWristSize, \"mm\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2393,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"range\",\n              min: 40,\n              max: 65,\n              value: userWristSize,\n              onChange: e => handleWristSizeChange(parseInt(e.target.value)),\n              style: styles.slider\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2396,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.sliderLabels,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"40mm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2405,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"65mm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2406,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2404,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.presetButtons,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(DEFAULT_WRIST_SIZE),\n                children: [\"Average (\", DEFAULT_WRIST_SIZE, \"mm)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2411,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(40),\n                children: \"Small (40mm)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2417,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(65),\n                children: \"Large (65mm)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2423,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2410,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2392,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2377,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2359,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2354,\n      columnNumber: 9\n    }, this), showProductSelection && /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: panelRef,\n      style: {\n        ...styles.productSelection,\n        transform: `translateY(${panelPosition}px)`,\n        touchAction: 'none'\n      },\n      className: isMobile ? 'mobile-product-panel' : '',\n      \"aria-modal\": \"true\",\n      role: \"dialog\",\n      onTouchStart: handleTouchStart,\n      onTouchMove: handleTouchMove,\n      onTouchEnd: handleTouchEnd,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.dragHandle,\n        \"aria-hidden\": \"true\",\n        onTouchStart: handleTouchStart,\n        onTouchMove: handleTouchMove,\n        onTouchEnd: handleTouchEnd\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2452,\n        columnNumber: 11\n      }, this), !urlParams.type && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.productTabs,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.tab,\n            ...(activeTab === 'Watches' ? styles.activeTab : {})\n          },\n          onClick: () => handleTabChange('Watches'),\n          children: \"Watches\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2462,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.tab,\n            ...(activeTab === 'Bracelets' ? styles.activeTab : {})\n          },\n          onClick: () => handleTabChange('Bracelets'),\n          children: \"Bracelets\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2471,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2461,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.productScroll,\n        className: \"product-scroll\",\n        children: getCurrentProducts().length > 0 ? getCurrentProducts().map((product, index) => {\n          // Simple null check only\n          if (!product) return null;\n          const isSelected = (typeof selectedProduct === 'object' ? selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.path : selectedProduct) === product.path;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              ...styles.productItem,\n              borderColor: isSelected ? '#2D8C88' : '#e9ecef',\n              backgroundColor: isSelected ? '#f0fffe' : '#ffffff'\n            },\n            title: `${product.name} - ${product.caseDiameter || 'N/A'}mm`,\n            onClick: () => handleProductSelect(product),\n            \"aria-label\": `Select ${product.name} ${product.caseDiameter || 'N/A'}mm`,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: product.path,\n              alt: product.name,\n              style: styles.productImage,\n              onError: e => {\n                e.target.parentElement.style.display = 'none';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2502,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.productLabel,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.productName,\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2511,\n                columnNumber: 23\n              }, this), activeTab === 'Watches' && product.caseDiameter && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.productSize,\n                children: [product.caseDiameter, \"mm\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2513,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2510,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2491,\n            columnNumber: 19\n          }, this);\n        }) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.noProductsMessage,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.noProductsIcon,\n            children: \"\\uD83D\\uDCF1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2521,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.noProductsTitle,\n            children: \"No Products Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2522,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.noProductsText,\n            children: \"This try-on experience is designed to be accessed through client websites with specific product parameters.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2523,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.noProductsSubtext,\n            children: \"Please visit a client's product page and click the \\\"Try On Virtually\\\" button.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2526,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2520,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2482,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2438,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1912,\n    columnNumber: 5\n  }, this);\n};\n\n// Styles object - Clean, modern design with improved mobile responsiveness\n_s(Tryon, \"Wc3xnFkgEMqFkmhRI1S7GduxqDQ=\");\n_c = Tryon;\nconst styles = {\n  container: {\n    position: 'relative',\n    height: 'calc(var(--vh, 1vh) * 100)',\n    display: 'flex',\n    flexDirection: 'column',\n    backgroundColor: '#f8f9fa',\n    color: '#333',\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    overflow: 'hidden',\n    touchAction: 'manipulation',\n    WebkitTapHighlightColor: 'transparent',\n    WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS\n  },\n  cameraContainer: {\n    flex: 1,\n    position: 'relative',\n    overflow: 'hidden',\n    backgroundColor: '#000',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  cameraFeed: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    transform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  capturedImage: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    display: 'none',\n    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  homeBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  backBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  switchContainer: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    zIndex: 10,\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '4px',\n    padding: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  switchTrack: {\n    position: 'absolute',\n    top: '12px',\n    left: '12px',\n    width: '60px',\n    height: '32px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    borderRadius: '16px',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    zIndex: 9,\n    transition: 'all 0.3s ease'\n  },\n  switchButton: {\n    position: 'relative',\n    width: '28px',\n    height: '28px',\n    borderRadius: '50%',\n    border: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 11,\n    margin: '2px',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    '&:hover': {\n      transform: 'scale(1.1)'\n    }\n  },\n  switchLabel: {\n    fontSize: '12px',\n    fontWeight: '700',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    marginTop: '4px',\n    padding: '4px 12px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    letterSpacing: '0.5px'\n  },\n  countdownDisplay: {\n    position: 'absolute',\n    top: '35%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '16px 24px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  countdownNumber: {\n    fontSize: '72px',\n    fontWeight: '900',\n    color: '#2D8C88',\n    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',\n    marginBottom: '8px',\n    animation: 'pulse 1s ease-in-out'\n  },\n  countdownText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'\n  },\n  statusMessage: {\n    position: 'absolute',\n    top: '25%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '12px 20px',\n    borderRadius: '16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  statusText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.9)',\n    padding: '12px 20px',\n    borderRadius: '25px',\n    marginBottom: '8px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtext: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '15px'\n  },\n  handGuide: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    width: '80%',\n    maxWidth: '400px',\n    height: 'auto',\n    opacity: 0.8,\n    pointerEvents: 'none',\n    zIndex: 5,\n    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    transition: 'all 0.3s ease'\n  },\n  productPosition: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 8,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '25vw',\n    // width controlled\n    aspectRatio: '1 / 1.6',\n    // maintain height-to-width ratio (adjust as needed)\n    minWidth: '100px',\n    minHeight: '160px',\n    // fallback for unsupported aspect-ratio\n    pointerEvents: 'none'\n  },\n  captureButtonContainer: {\n    position: 'absolute',\n    bottom: '30px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    display: 'flex',\n    alignItems: 'flex-end',\n    gap: '30px',\n    zIndex: 10\n  },\n  captureButtonWrapper: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '8px'\n  },\n  modelButtonWrapper: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '8px'\n  },\n  buttonLabel: {\n    fontSize: '12px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 3px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    padding: '4px 8px',\n    borderRadius: '12px',\n    whiteSpace: 'nowrap',\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)'\n  },\n  captureBtn: {\n    width: '80px',\n    height: '80px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '4px solid rgba(255, 255, 255, 0.3)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0,\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  captureInner: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: '#2D8C88',\n    borderRadius: '50%',\n    transition: 'all 0.2s ease'\n  },\n  modelBtn: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: 'rgba(45, 140, 136, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '3px solid rgba(255, 255, 255, 0.3)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0,\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  resetBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '50px',\n    height: '50px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n  // Wrist Size Floating Button - Follows CSS mobile patterns\n  wristSizeFloatingBtn: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    padding: '12px 16px',\n    borderRadius: '24px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    border: 'none',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    outline: 'none',\n    transition: 'all 0.2s ease',\n    zIndex: 15,\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)',\n    minHeight: '48px',\n    minWidth: '48px',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  wristSizeText: {\n    fontSize: '12px',\n    fontWeight: '600'\n  },\n  // Modal Styles\n  modalOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 50,\n    padding: '20px',\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)',\n    touchAction: 'none'\n  },\n  wristSizeModal: {\n    backgroundColor: 'white',\n    borderRadius: '24px',\n    width: '100%',\n    maxWidth: '95vw',\n    maxHeight: '80vh',\n    overflow: 'hidden',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',\n    display: 'flex',\n    flexDirection: 'column',\n    margin: '10px',\n    position: 'relative'\n  },\n  modalHeader: {\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    padding: '16px 20px 12px 20px',\n    borderBottom: '1px solid #f0f0f0'\n  },\n  modalTitle: {\n    fontSize: '18px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0\n  },\n  modalCloseBtn: {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    border: 'none',\n    backgroundColor: '#f5f5f5',\n    color: '#666',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'all 0.2s ease',\n    outline: 'none'\n  },\n  modalContent: {\n    padding: '16px 20px 20px 20px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '16px',\n    overflowY: 'auto',\n    WebkitOverflowScrolling: 'touch',\n    maxHeight: 'calc(80vh - 60px)'\n  },\n  // Mobile styles handled by CSS file\n  wristSizeContent: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '12px'\n  },\n  wristSizeTitle: {\n    fontSize: '20px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0,\n    textAlign: 'center'\n  },\n  wristSizeSubtitle: {\n    fontSize: '14px',\n    color: '#666',\n    margin: 0,\n    textAlign: 'center',\n    lineHeight: '1.4'\n  },\n  genderSelection: {\n    display: 'flex',\n    gap: '12px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  genderButton: {\n    flex: 1,\n    padding: '12px 16px',\n    borderRadius: '10px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '2px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  genderButtonActive: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderColor: '#2D8C88',\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'\n  },\n  sliderContainer: {\n    width: '100%',\n    maxWidth: '300px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '8px'\n  },\n  sliderLabel: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: '#333',\n    textAlign: 'center',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px'\n  },\n  sizeChange: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    backgroundColor: 'rgba(45, 140, 136, 0.1)',\n    padding: '2px 8px',\n    borderRadius: '12px'\n  },\n  slider: {\n    width: '100%',\n    height: '6px',\n    borderRadius: '3px',\n    background: '#e0e0e0',\n    outline: 'none',\n    cursor: 'pointer',\n    WebkitAppearance: 'none',\n    appearance: 'none'\n  },\n  sliderLabels: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    fontSize: '12px',\n    color: '#999',\n    marginTop: '4px'\n  },\n  presetButtons: {\n    display: 'flex',\n    gap: '8px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  presetButton: {\n    flex: 1,\n    padding: '8px 12px',\n    borderRadius: '8px',\n    fontSize: '12px',\n    fontWeight: '500',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  continueButton: {\n    width: '100%',\n    maxWidth: '300px',\n    padding: '14px 24px',\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderRadius: '12px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n  // Fancy Popup Styles\n  fancyPopupModal: {\n    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',\n    borderRadius: '24px',\n    width: '100%',\n    maxWidth: '380px',\n    overflow: 'hidden',\n    boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 32px rgba(45, 140, 136, 0.1)',\n    display: 'flex',\n    flexDirection: 'column',\n    margin: '10px',\n    position: 'relative',\n    border: '1px solid rgba(45, 140, 136, 0.1)'\n  },\n  fancyPopupHeader: {\n    textAlign: 'center',\n    padding: '32px 24px 24px 24px',\n    background: 'linear-gradient(135deg, #2D8C88 0%, #1a6b67 100%)',\n    color: 'white',\n    position: 'relative'\n  },\n  fancyPopupTitle: {\n    fontSize: '24px',\n    fontWeight: '700',\n    marginBottom: '8px',\n    textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'\n  },\n  fancyPopupSubtitle: {\n    fontSize: '14px',\n    fontWeight: '400',\n    opacity: 0.9,\n    letterSpacing: '0.5px'\n  },\n  fancyPopupContent: {\n    padding: '24px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '20px'\n  },\n  fancyPopupPoint: {\n    display: 'flex',\n    alignItems: 'flex-start',\n    gap: '16px'\n  },\n  fancyPointNumber: {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    background: 'linear-gradient(135deg, #2D8C88 0%, #1a6b67 100%)',\n    color: 'white',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontSize: '14px',\n    fontWeight: '700',\n    flexShrink: 0,\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n  fancyPointText: {\n    fontSize: '15px',\n    lineHeight: '1.5',\n    color: '#333',\n    flex: 1,\n    paddingTop: '4px'\n  },\n  fancyOkButton: {\n    position: 'relative',\n    width: '100%',\n    padding: '16px 24px',\n    background: 'linear-gradient(135deg, #2D8C88 0%, #1a6b67 100%)',\n    color: '#ffffff',\n    borderRadius: '0 0 24px 24px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    border: 'none',\n    outline: 'none',\n    overflow: 'hidden'\n  },\n  fancyButtonText: {\n    position: 'relative',\n    zIndex: 2\n  },\n  fancyButtonGlow: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%)',\n    opacity: 0,\n    transition: 'opacity 0.3s ease'\n  },\n  // Clean Instruction Styles\n  instructionContainer: {\n    position: 'absolute',\n    top: '28%',\n    // moved further down from 20%\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 19,\n    pointerEvents: 'none'\n  },\n  instructionText: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: 'white',\n    textAlign: 'center',\n    padding: '8px 16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '12px',\n    backdropFilter: 'blur(8px)',\n    WebkitBackdropFilter: 'blur(8px)',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)',\n    border: '1px solid rgba(255, 255, 255, 0.1)',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)'\n  },\n  productSelection: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.98)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '24px',\n    borderTopRightRadius: '24px',\n    padding: '16px',\n    maxHeight: '85vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 20,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none',\n    transform: 'translateY(0)',\n    transition: 'transform 0.3s ease-out',\n    overflow: 'hidden',\n    touchAction: 'none',\n    willChange: 'transform',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n  closeBtn: {\n    position: 'absolute',\n    top: '12px',\n    right: '16px',\n    color: '#666',\n    cursor: 'pointer',\n    zIndex: 21,\n    width: '32px',\n    height: '32px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderRadius: '50%',\n    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    padding: 0\n  },\n  productTabs: {\n    display: 'flex',\n    marginBottom: '16px',\n    backgroundColor: '#f0f0f0',\n    borderRadius: '12px',\n    padding: '4px',\n    gap: '4px'\n  },\n  tab: {\n    flex: 1,\n    textAlign: 'center',\n    padding: '12px 16px',\n    borderRadius: '8px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    color: '#666',\n    outline: 'none',\n    border: 'none',\n    backgroundColor: 'transparent',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  activeTab: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'\n  },\n  productScroll: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(100px, 1fr))',\n    gap: '12px',\n    maxHeight: 'calc(85vh - 120px)',\n    overflowY: 'auto',\n    paddingBottom: '16px',\n    scrollbarWidth: 'thin',\n    scrollbarColor: '#ddd #f5f5f5',\n    WebkitOverflowScrolling: 'touch'\n  },\n  productItem: {\n    position: 'relative',\n    width: '100%',\n    aspectRatio: '1/1',\n    backgroundColor: '#ffffff',\n    borderRadius: '16px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    overflow: 'hidden',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',\n    padding: '8px',\n    outline: 'none',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  productImage: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'contain',\n    borderRadius: '8px',\n    backgroundColor: '#ffffff'\n  },\n  productLabel: {\n    position: 'absolute',\n    bottom: '3px',\n    left: '3px',\n    right: '3px',\n    fontSize: '9px',\n    color: '#666',\n    textAlign: 'center',\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    borderRadius: '3px',\n    padding: '3px 2px',\n    overflow: 'hidden'\n  },\n  productName: {\n    fontSize: '9px',\n    fontWeight: '600',\n    whiteSpace: 'nowrap',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    marginBottom: '1px'\n  },\n  productSize: {\n    fontSize: '8px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    whiteSpace: 'nowrap'\n  },\n  dragHandle: {\n    width: '40px',\n    height: '4px',\n    backgroundColor: '#e0e0e0',\n    borderRadius: '2px',\n    margin: '0 auto 12px',\n    cursor: 'grab',\n    touchAction: 'none',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n  desktopContainer: {\n    position: 'relative',\n    height: '100vh',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    backgroundColor: '#f8f9fa',\n    padding: '20px'\n  },\n  qrContainer: {\n    backgroundColor: 'white',\n    padding: '40px',\n    borderRadius: '24px',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',\n    textAlign: 'center',\n    maxWidth: '500px',\n    width: '100%'\n  },\n  qrTitle: {\n    fontSize: '28px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    marginBottom: '16px'\n  },\n  qrSubtitle: {\n    fontSize: '16px',\n    color: '#666',\n    marginBottom: '32px',\n    lineHeight: '1.5'\n  },\n  qrWrapper: {\n    backgroundColor: 'white',\n    padding: '20px',\n    borderRadius: '16px',\n    display: 'inline-block',\n    marginBottom: '24px',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'\n  },\n  qrLink: {\n    fontSize: '14px',\n    color: '#2D8C88',\n    marginBottom: '32px',\n    wordBreak: 'break-all'\n  },\n  clientInfo: {\n    fontSize: '12px',\n    color: '#666',\n    marginBottom: '16px',\n    fontStyle: 'italic'\n  },\n  noProductsMessage: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n    padding: '40px 20px',\n    textAlign: 'center',\n    height: '200px'\n  },\n  noProductsIcon: {\n    fontSize: '48px',\n    marginBottom: '16px'\n  },\n  noProductsTitle: {\n    fontSize: '18px',\n    fontWeight: '600',\n    color: '#333',\n    marginBottom: '12px'\n  },\n  noProductsText: {\n    fontSize: '14px',\n    color: '#666',\n    lineHeight: '1.5',\n    marginBottom: '8px',\n    maxWidth: '280px'\n  },\n  noProductsSubtext: {\n    fontSize: '12px',\n    color: '#999',\n    lineHeight: '1.4',\n    maxWidth: '260px'\n  },\n  // Disclaimer Popup Styles\n  disclaimerOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.7)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 1000,\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)'\n  },\n  disclaimerPopup: {\n    backgroundColor: 'white',\n    borderRadius: '20px',\n    padding: '0',\n    maxWidth: '400px',\n    width: '90%',\n    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.2)'\n  },\n  disclaimerContent: {\n    padding: '32px 24px',\n    textAlign: 'center'\n  },\n  disclaimerTitle: {\n    fontSize: '24px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    marginBottom: '24px',\n    margin: '0 0 24px 0'\n  },\n  disclaimerPoints: {\n    marginBottom: '32px'\n  },\n  disclaimerPoint: {\n    fontSize: '16px',\n    color: '#333',\n    lineHeight: '1.6',\n    marginBottom: '16px',\n    margin: '0 0 16px 0',\n    textAlign: 'center'\n  },\n  disclaimerButton: {\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    border: 'none',\n    borderRadius: '12px',\n    padding: '14px 32px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)',\n    ':hover': {\n      backgroundColor: '#258A86',\n      transform: 'translateY(-1px)',\n      boxShadow: '0 6px 16px rgba(45, 140, 136, 0.4)'\n    }\n  },\n  // Clean Auto Capture Toggle\n  autoCaptureToggle: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    zIndex: 20,\n    padding: '8px 12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '16px',\n    backdropFilter: 'blur(8px)',\n    WebkitBackdropFilter: 'blur(8px)',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  autoCaptureLabel: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)'\n  },\n  // Clean Branding Styles\n  brandingContainer: {\n    position: 'absolute',\n    top: '80px',\n    left: '20px',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '6px',\n    zIndex: 15,\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '16px',\n    backdropFilter: 'blur(8px)',\n    WebkitBackdropFilter: 'blur(8px)',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  brandingLogo: {\n    width: '22px',\n    height: '22px',\n    objectFit: 'contain',\n    marginRight: '7px',\n    display: 'inline-block',\n    verticalAlign: 'middle'\n  },\n  brandingText: {\n    fontSize: '11px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)'\n  },\n  // Add new styles for smaller status message\n  statusMessageSmall: {\n    position: 'absolute',\n    top: '13%',\n    // move up so it's above the SVG shape\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '6px 12px',\n    borderRadius: '10px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(8px)',\n    WebkitBackdropFilter: 'blur(8px)',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'\n  },\n  statusTextSmall: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.8)',\n    padding: '6px 12px',\n    borderRadius: '15px',\n    marginBottom: '4px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtextSmall: {\n    fontSize: '10px',\n    fontWeight: '400',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    padding: '4px 8px',\n    borderRadius: '10px'\n  },\n  // Clean Branding - Tangiblee style\n  brandingContainerTangiblee: {\n    position: 'absolute',\n    top: '32px',\n    left: '12px',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '14px',\n    zIndex: 15,\n    backgroundColor: 'rgba(0,0,0,0.0)',\n    // fully transparent\n    padding: '0',\n    borderRadius: '0',\n    boxShadow: 'none'\n  },\n  brandingLogoTangiblee: {\n    width: '38px',\n    height: '38px',\n    objectFit: 'contain',\n    display: 'inline-block',\n    verticalAlign: 'middle'\n  },\n  brandingTextStacked: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'flex-start',\n    lineHeight: 1.1\n  },\n  poweredByText: {\n    fontSize: '15px',\n    color: 'white',\n    fontWeight: 400,\n    opacity: 0.85,\n    letterSpacing: '0.01em',\n    marginBottom: '1px'\n  },\n  viatryonText: {\n    fontSize: '28px',\n    color: 'white',\n    fontWeight: 600,\n    letterSpacing: '0.01em',\n    marginTop: '0'\n  }\n};\nexport default Tryon;\nvar _c;\n$RefreshReg$(_c, \"Tryon\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "QRCodeSVG", "uShapeCutter", "removeProductBackground", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "sliderCSS", "document", "getElementById", "styleElement", "createElement", "id", "textContent", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "onBackToHome", "_s", "videoRef", "capturedImageRef", "canvasRef", "isCaptured", "setIsCaptured", "selectedProduct", "setSelectedProduct", "isRightHand", "setIsRightHand", "showProductSelection", "setShowProductSelection", "activeTab", "setActiveTab", "showHandGuide", "setShowHandGuide", "isMobile", "setIsMobile", "isDesktop", "setIsDesktop", "showDisclaimerPopup", "setShowDisclaimerPopup", "isUsingModelImage", "setIsUsingModelImage", "urlParams", "setUrlParams", "image", "client", "size", "type", "sessionId", "setSessionId", "sessionStartTime", "setSessionStartTime", "interactions", "setInteractions", "behaviorMetrics", "setBehaviorMetrics", "timeToFirstInteraction", "productSwitches", "backgroundRemovalSuccess", "handDetectionSuccess", "totalInteractions", "cameraInitSuccess", "engagementScore", "scrollDepth", "mouseMovements", "hoverEvents", "gestureEvents", "featureUsage", "cameraToggle", "productRotation", "sizeAdjustment", "colorChange", "screenshot", "share", "zoom", "exitIntent", "detected", "timestamp", "beforeConversion", "attentionMetrics", "focusTime", "blurEvents", "returnEvents", "idleTime", "performanceMetrics", "setPerformanceMetrics", "frameRate", "average", "min", "max", "drops", "memoryUsage", "used", "total", "networkMetrics", "renderMetrics", "resourceLoadTimes", "qualityMetrics", "setQualityMetrics", "handDetectionAccuracy", "backgroundRemovalQuality", "productFitAccuracy", "userSatisfactionScore", "technicalIssues", "conversionFunnel", "setConversionFunnel", "viewedProduct", "initiatedTryOn", "completedTryOn", "sharedResult", "addedToCart", "proceededToCheckout", "completedPurchase", "userGender", "setUserGender", "userWristSize", "setUserWristSize", "showWristSizeModal", "setShowWristSizeModal", "isAutoCaptureEnabled", "setIsAutoCaptureEnabled", "countdown", "setCountdown", "isHandInPosition", "setIsHandInPosition", "isCountdownActive", "setIsCountdownActive", "panelPosition", "setPanelPosition", "isDragging", "setIsDragging", "startY", "setStartY", "panelRef", "watches", "name", "path", "caseDiameter", "caseThickness", "totalWidth", "totalHeight", "dialDiameter", "dialSize", "bracelets", "removeBackground", "imgElement", "productType", "processedImageUrl", "src", "console", "log", "error", "warn", "basicBackgroundRemoval", "finalProcessedUrl", "style", "filter", "mixBlendMode", "opacity", "uShapedImage", "canvas", "ctx", "getContext", "width", "naturalWidth", "height", "naturalHeight", "drawImage", "imageData", "getImageData", "data", "i", "length", "r", "g", "b", "threshold", "Math", "abs", "putImageData", "toDataURL", "detectHandOrientation", "random", "initCamera", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "facingMode", "ideal", "current", "srcObject", "err", "display", "<PERSON><PERSON><PERSON><PERSON>", "videoWidth", "videoHeight", "parseUrlParams", "urlSearchParams", "URLSearchParams", "window", "location", "search", "params", "get", "imageUrl", "URL", "defaultSize", "actualSize", "parseInt", "<PERSON><PERSON><PERSON><PERSON>", "clientId", "startAnalyticsSession", "cleanup", "initializeEnhancedTracking", "prev", "handleBeforeUnload", "endAnalyticsSession", "handleVisibilityChange", "visibilityState", "addEventListener", "removeEventListener", "hasClient", "hasImage", "checkDevice", "isMobileDevice", "innerWidth", "setVH", "vh", "innerHeight", "documentElement", "setProperty", "setTimeout", "DEFAULT_WRIST_SIZE", "MIN_WRIST_SIZE", "MAX_WRIST_SIZE", "ASSUMED_DIAL_SIZE", "DEFAULT_WRIST_SIZES", "men", "WRIST_SIZE_OFFSET", "MIN_ADJUSTED_WRIST_SIZE", "SVG_WRIST_CIRCLE_DIAMETER", "SVG_VIEWBOX_WIDTH", "SVG_VIEWBOX_HEIGHT", "WATCH_WIDTH", "BRACELET_WIDTH", "WATCH_HEIGHT", "BRACELET_HEIGHT", "calculateWatchDimensions", "watch", "containerWidth", "containerHeight", "defaultWristSize", "adjustedWristSize", "inverseWristSizeRatio", "mmToSvgScale", "watchWidthSvg", "watchHeightSvg", "dialDiameterSvg", "watchWidthPercent", "watchHeightPercent", "dialDiameterPercent", "positionX", "positionY", "scale", "realWidth", "realHeight", "wristSizeRatio", "getWatchPosition", "watchData", "baseDimensions", "adjustedX", "adjustedY", "svgHeight", "watchHeight", "scaleToFitHeight", "mouseDistance", "lastMousePos", "x", "y", "handleMouseMove", "e", "distance", "sqrt", "pow", "clientX", "clientY", "handleScroll", "scrollTop", "pageYOffset", "doc<PERSON><PERSON>ght", "scrollHeight", "scrollPercent", "round", "trackInteraction", "depth", "position", "focusStartTime", "Date", "now", "isPageFocused", "handleFocus", "handleBlur", "handleMouseLeave", "monitorPerformance", "performance", "memory", "usedJSHeapSize", "totalJSHeapSize", "connection", "connectionType", "downlink", "rtt", "effectiveType", "performanceInterval", "setInterval", "clearInterval", "getUserIP", "response", "fetch", "json", "ip", "pageLoadStartTime", "userIP", "sessionData", "productId", "productName", "productCategory", "device", "screenResolution", "screen", "os", "platform", "browser", "userAgent", "split", "pop", "timezone", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "referrer", "productViewTimes", "duration", "pageLoadTime", "apiResponseTimes", "errors", "baseUrl", "process", "env", "REACT_APP_API_URL", "origin", "apiUrl", "replace", "method", "headers", "body", "JSON", "stringify", "ok", "result", "trackPerformanceMetric", "errorData", "trackError", "message", "interaction", "updateBehaviorMetric", "element", "intensity", "sequence", "context", "metric", "value", "catch", "newMetrics", "includes", "parent", "child", "outcome", "endTime", "floor", "updateData", "detectHandInPosition", "videoContainer", "parentElement", "containerRect", "getBoundingClientRect", "videoAspect", "containerAspect", "displayWidth", "displayHeight", "offsetX", "offsetY", "scaleX", "scaleY", "rectX", "rectY", "rectWidth", "rectHeight", "circleX", "circleY", "circleWidth", "circleHeight", "rectImageData", "rectData", "circleImageData", "circleData", "rectSkinPixels", "rectTotalPixels", "circleSkinPixels", "circleTotalPixels", "isSkinTone", "condition1", "condition2", "condition3", "condition4", "rectSkinRatio", "circleSkinRatio", "wristInPosition", "handInPosition", "applyProductToWatchPosition", "productPath", "find", "w", "dimensions", "takeScreenshot", "toBlob", "blob", "url", "createObjectURL", "a", "href", "download", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "sessionDuration", "handleZoom", "zoomLevel", "handleShare", "title", "text", "clipboard", "writeText", "alert", "handleTryOnModel", "modelImagePath", "onload", "detectWristOnModelImage", "hasProduct", "img", "palmPosition", "detectPalmPositionOnModel", "modelPalmPosition", "palmDetected", "success", "wristY", "wristX", "min<PERSON><PERSON><PERSON>", "endY", "leftEdge", "rightEdge", "pixelIndex", "alpha", "isHandPixel", "armWidth", "wristBounds", "centerX", "centerY", "confidence", "isModelHand", "handleCapture", "capturedDataUrl", "handleBack", "handleGenderChange", "gender", "newWristSize", "handleWristSizeChange", "handleTabChange", "tabName", "handleAutoCaptureToggle", "newState", "handleBackWithReset", "getCurrentProducts", "handleProductSelect", "product", "handleDisclaimerClose", "interval", "countdownInterval", "handleTouchStart", "touches", "handleTouchMove", "currentY", "diff", "handleTouchEnd", "handleClickOutside", "target", "closest", "DesktopQRCode", "generateQRValue", "websiteUrl", "REACT_APP_WEBSITE_URL", "append", "toString", "qrValue", "styles", "desktopContainer", "children", "qr<PERSON><PERSON><PERSON>", "qrTitle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "qrSubtitle", "qrWrapper", "level", "bgColor", "fgColor", "qrLink", "clientInfo", "homeBtn", "onClick", "container", "disclaimer<PERSON><PERSON><PERSON>", "disclaimerPopup", "disclaimerContent", "disclaimerTitle", "disclaimerPoints", "disclaimerPoint", "disclaimer<PERSON><PERSON><PERSON>", "brandingContainerTangiblee", "alt", "brandingLogoTangiblee", "onError", "brandingTextStacked", "poweredByText", "color", "viatryonText", "cameraContainer", "ref", "cameraFeed", "autoPlay", "playsInline", "muted", "capturedImage", "autoCaptureToggle", "autoCaptureLabel", "className", "checked", "onChange", "disabled", "countdownDisplay", "countdownNumber", "countdownText", "instructionContainer", "instructionText", "statusMessageSmall", "statusTextSmall", "statusSubtextSmall", "statusMessage", "statusText", "backgroundColor", "handGuide", "viewBox", "xmlns", "d", "stroke", "strokeWidth", "fill", "strokeLinecap", "rx", "cx", "cy", "textAnchor", "fontSize", "fontWeight", "productPosition", "top", "transform", "isLargeWrist", "sizeIncrease", "clipPath", "overflow", "alignItems", "justifyContent", "objectFit", "baseTransform", "_selectedProduct$dime", "heightScale", "widthScale", "onLoad", "bottom", "left", "padding", "borderRadius", "whiteSpace", "pointerEvents", "boxShadow", "zIndex", "marginLeft", "scalingPercentage", "sizeDifference", "maxSizeDifference", "clampedDifference", "moderateScaleFactor", "toFixed", "debugSuffix", "captureButtonContainer", "captureButtonWrapper", "captureBtn", "captureInner", "buttonLabel", "modelButtonWrapper", "modelBtn", "resetBtn", "reload", "wristSizeFloatingBtn", "wristSizeText", "modalOverlay", "wristSizeModal", "stopPropagation", "modalHeader", "modalTitle", "modalCloseBtn", "modalContent", "genderSelection", "genderButton", "genderButtonActive", "slide<PERSON><PERSON><PERSON><PERSON>", "slider<PERSON><PERSON><PERSON>", "slider", "slider<PERSON><PERSON><PERSON>", "presetButtons", "presetButton", "productSelection", "touchAction", "role", "onTouchStart", "onTouchMove", "onTouchEnd", "dragHandle", "productTabs", "tab", "productScroll", "map", "index", "isSelected", "productItem", "borderColor", "productImage", "productLabel", "productSize", "noProductsMessage", "noProductsIcon", "noProductsTitle", "noProductsText", "noProductsSubtext", "_c", "flexDirection", "fontFamily", "WebkitTapHighlightColor", "WebkitOverflowScrolling", "flex", "WebkitTransform", "cursor", "border", "transition", "outline", "backBtn", "switchContainer", "right", "gap", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "switchTrack", "switchButton", "margin", "switchLabel", "textShadow", "marginTop", "letterSpacing", "textAlign", "marginBottom", "animation", "statusSubtext", "max<PERSON><PERSON><PERSON>", "WebkitFilter", "aspectRatio", "minHeight", "maxHeight", "borderBottom", "overflowY", "wristSizeContent", "wristSizeTitle", "wristSizeSubtitle", "lineHeight", "sizeChange", "background", "WebkitAppearance", "appearance", "continueButton", "fancyPopupModal", "fancyPopupHeader", "fancyPopupTitle", "fancyPopupSubtitle", "fancy<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "fancyPopupPoint", "fancyPointNumber", "flexShrink", "fancyPointText", "paddingTop", "fancyOkButton", "fancyButtonText", "fancyButtonGlow", "borderTopLeftRadius", "borderTopRightRadius", "<PERSON><PERSON><PERSON><PERSON>", "userSelect", "WebkitUserSelect", "closeBtn", "gridTemplateColumns", "paddingBottom", "scrollbarWidth", "scrollbarColor", "textOverflow", "wordBreak", "fontStyle", "brandingContainer", "brandingLogo", "marginRight", "verticalAlign", "brandingText", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/Tryon.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { QRCodeSVG } from 'qrcode.react'; // Fix the import\nimport uShape<PERSON>utter from '../utils/uShapeCutter';\nimport { removeProductBackground } from '../utils/backgroundRemover';\n\n// Add CSS for range slider styling\nconst sliderCSS = `\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    border: none;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-webkit-slider-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n  }\n\n  input[type=\"range\"]::-moz-range-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n    border: none;\n  }\n\n  input[type=\"range\"]:focus {\n    outline: none;\n  }\n\n  input[type=\"range\"]:focus::-webkit-slider-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  input[type=\"range\"]:focus::-moz-range-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  /* Clean Switch styles */\n  .switch-container {\n    position: relative;\n    display: inline-block;\n    width: 40px;\n    height: 22px;\n  }\n\n  .switch-container input {\n    opacity: 0;\n    width: 0;\n    height: 0;\n  }\n\n  .switch-slider {\n    position: absolute;\n    cursor: pointer;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(255, 255, 255, 0.3);\n    transition: .3s;\n    border-radius: 22px;\n    border: 1px solid rgba(255, 255, 255, 0.2);\n  }\n\n  .switch-slider:before {\n    position: absolute;\n    content: \"\";\n    height: 16px;\n    width: 16px;\n    left: 2px;\n    bottom: 2px;\n    background-color: white;\n    transition: .3s;\n    border-radius: 50%;\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\n  }\n\n  input:checked + .switch-slider {\n    background-color: #2D8C88;\n  }\n\n  input:checked + .switch-slider:before {\n    transform: translateX(18px);\n  }\n\n  input:disabled + .switch-slider {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n`;\n\n// Inject CSS (only once)\nif (typeof document !== 'undefined' && !document.getElementById('wrist-size-slider-styles')) {\n  const styleElement = document.createElement('style');\n  styleElement.id = 'wrist-size-slider-styles';\n  styleElement.textContent = sliderCSS;\n  document.head.appendChild(styleElement);\n}\n\nconst Tryon = ({ onBackToHome }) => {\n  // Refs for DOM elements\n  const videoRef = useRef(null);\n  const capturedImageRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // State variables\n  const [isCaptured, setIsCaptured] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [isRightHand, setIsRightHand] = useState(false);\n  const [showProductSelection, setShowProductSelection] = useState(false);\n  const [activeTab, setActiveTab] = useState('Watches');\n  const [showHandGuide, setShowHandGuide] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n  const [isDesktop, setIsDesktop] = useState(false);\n  const [showDisclaimerPopup, setShowDisclaimerPopup] = useState(true);\n  const [isUsingModelImage, setIsUsingModelImage] = useState(false);\n\n  // URL parameters state\n  const [urlParams, setUrlParams] = useState({\n    image: null,\n    client: null,\n    size: null,\n    type: null\n  });\n\n  // Analytics tracking state\n  const [sessionId, setSessionId] = useState(null);\n  const [sessionStartTime, setSessionStartTime] = useState(null);\n  const [interactions, setInteractions] = useState([]);\n  const [behaviorMetrics, setBehaviorMetrics] = useState({\n    timeToFirstInteraction: null,\n    productSwitches: 0,\n    backgroundRemovalSuccess: false,\n    handDetectionSuccess: false,\n    totalInteractions: 0,\n    cameraInitSuccess: false,\n    engagementScore: 0,\n    scrollDepth: 0,\n    mouseMovements: 0,\n    hoverEvents: [],\n    gestureEvents: [],\n    featureUsage: {\n      cameraToggle: 0,\n      productRotation: 0,\n      sizeAdjustment: 0,\n      colorChange: 0,\n      screenshot: 0,\n      share: 0,\n      zoom: 0\n    },\n    exitIntent: {\n      detected: false,\n      timestamp: null,\n      beforeConversion: false\n    },\n    attentionMetrics: {\n      focusTime: 0,\n      blurEvents: 0,\n      returnEvents: 0,\n      idleTime: 0\n    }\n  });\n\n  // Enhanced tracking state\n  const [performanceMetrics, setPerformanceMetrics] = useState({\n    frameRate: { average: 0, min: 0, max: 0, drops: 0 },\n    memoryUsage: { used: 0, total: 0 },\n    networkMetrics: {},\n    renderMetrics: {},\n    resourceLoadTimes: []\n  });\n  const [qualityMetrics, setQualityMetrics] = useState({\n    handDetectionAccuracy: 0,\n    backgroundRemovalQuality: 0,\n    productFitAccuracy: 0,\n    userSatisfactionScore: 0,\n    technicalIssues: []\n  });\n  const [conversionFunnel, setConversionFunnel] = useState({\n    viewedProduct: true,\n    initiatedTryOn: false,\n    completedTryOn: false,\n    sharedResult: false,\n    addedToCart: false,\n    proceededToCheckout: false,\n    completedPurchase: false\n  });\n\n  // Watch size customization state\n  const [userGender, setUserGender] = useState('men'); // Single gender role\n  const [userWristSize, setUserWristSize] = useState(50); // Default wrist size in mm\n  const [showWristSizeModal, setShowWristSizeModal] = useState(false); // Mobile-friendly modal\n\n  // Autocapture state variables\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [isHandInPosition, setIsHandInPosition] = useState(false);\n  const [isCountdownActive, setIsCountdownActive] = useState(false);\n\n  // Add new state for panel position\n  const [panelPosition, setPanelPosition] = useState(0);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startY, setStartY] = useState(0);\n  const panelRef = useRef(null);\n\n  // Realistic watch data with actual dimensions (in millimeters)\n  const watches = [\n    {\n      name: \"Classic Black\",\n      path: \"imgs/watches/watch_1.png\",\n      // Rolex Submariner style - 40mm case\n      caseDiameter: 41, // mm\n      caseThickness: 12.5, // mm\n      totalWidth: 42, // mm (including crown)\n      totalHeight: 47, // mm (lug to lug)\n      dialDiameter: 31, // mm (visible dial)\n      type: \"dress\",\n      dialSize: 40\n    },\n    {\n      name: \"Silver Chrono\",\n      path: \"watches/watch_2.png\",\n      // Omega Speedmaster style - 42mm case\n      caseDiameter: 42, // mm\n      caseThickness: 13.2, // mm\n      totalWidth: 44, // mm\n      totalHeight: 48.5, // mm\n      dialDiameter: 33, // mm\n      type: \"sport\",\n      dialSize: 42\n    },\n    {\n      name: \"Gold Luxury\",\n      path: \"watches/watch_3.png\",\n      // Patek Philippe Calatrava style - 38mm case\n      caseDiameter: 39, // mm\n      caseThickness: 8.5, // mm\n      totalWidth: 39, // mm\n      totalHeight: 45, // mm\n      dialDiameter: 30, // mm\n      type: \"luxury\",\n      dialSize: 38\n    },\n    {\n      name: \"Sport Blue\",\n      path: \"watches/watch_6.png\",\n      // Apple Watch style - 44mm case\n      caseDiameter: 41, // mm (width)\n      caseThickness: 10.7, // mm\n      totalWidth: 44, // mm\n      totalHeight: 38, // mm (height - rectangular)\n      dialDiameter: 35, // mm (screen diagonal)\n      type: \"smartwatch\",\n      dialSize: 44\n    },\n    {\n      name: \"Minimalist\",\n      path: \"watches/watch_5.png\",\n      // Daniel Wellington style - 36mm case\n      caseDiameter: 36, // mm\n      caseThickness: 6, // mm\n      totalWidth: 37, // mm\n      totalHeight: 43, // mm\n      dialDiameter: 28, // mm\n      type: \"minimalist\",\n      dialSize: 36\n    },\n    {\n      name: \"Rose Gold\",\n      path: \"watches/watch_4.png\",\n      // Michael Kors style - 39mm case\n      caseDiameter: 44, // mm\n      caseThickness: 11, // mm\n      totalWidth: 41, // mm\n      totalHeight: 46, // mm\n      dialDiameter: 31, // mm\n      type: \"fashion\",\n      dialSize: 41\n    }\n  ];\n\n  const bracelets = [\n    { name: \"Silver Chain\", path: \"bracelets/bracelet_1.png\" },\n    { name: \"Gold Bangle\", path: \"bracelets/bracelet_2.png\" },\n    { name: \"Leather Wrap\", path: \"bracelets/bracelet_3.png\" },\n    { name: \"Diamond Tennis\", path: \"bracelets/bracelet_4.png\" },\n    { name: \"Beaded Stone\", path: \"bracelets/bracelet_5.png\" },\n    { name: \"Charm Bracelet\", path: \"bracelets/bracelet_6.png\" }\n  ];\n\n  // Enhanced background removal function with proper bracelet processing sequence\n  const removeBackground = async (imgElement, productType = 'watch') => {\n    try {\n      let processedImageUrl = imgElement.src;\n\n      // For bracelets: Apply U-shape cutter → Background removal → Vertical flip\n      if (productType === 'bracelet') {\n        try {\n          // Step 1: Apply U-shape cutter first\n          processedImageUrl = await uShapeCutter(imgElement.src);\n          console.log('U-shape cutting applied successfully for bracelet');\n\n          // Step 2: Remove background after U-shape cutting\n          processedImageUrl = await removeProductBackground(processedImageUrl, productType);\n          console.log('Background removal applied after U-shape cutting');\n\n          // Step 3: Apply vertical flip based on hand detection (will be handled in transform)\n          imgElement.src = processedImageUrl;\n\n        } catch (error) {\n          console.warn('Bracelet processing sequence failed:', error);\n          // Fallback to basic processing\n          await basicBackgroundRemoval(imgElement, productType);\n        }\n      } else {\n        // For watches: Just apply background removal\n        const finalProcessedUrl = await removeProductBackground(processedImageUrl, productType);\n        imgElement.src = finalProcessedUrl;\n      }\n\n      // Apply product-specific styling that preserves all colors\n      imgElement.style.filter = 'none';\n      imgElement.style.mixBlendMode = 'normal';\n      imgElement.style.opacity = '1';\n\n      console.log(`Background removal completed for ${productType}`);\n\n    } catch (error) {\n      console.warn('Enhanced background removal failed:', error);\n      // Fallback to basic background removal\n      await basicBackgroundRemoval(imgElement, productType);\n    }\n  };\n\n  // Fallback basic background removal\n  const basicBackgroundRemoval = async (imgElement, productType) => {\n    try {\n      console.log(`Applying fallback background removal for ${productType}`);\n\n      if (productType === 'bracelet') {\n        // Apply U-shape cutter for bracelets\n        const uShapedImage = await uShapeCutter(imgElement.src);\n        imgElement.src = uShapedImage;\n      }\n\n      // Basic background removal with conservative settings\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.width = imgElement.naturalWidth;\n      canvas.height = imgElement.naturalHeight;\n      ctx.drawImage(imgElement, 0, 0);\n\n      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n      const data = imageData.data;\n\n      // Process each pixel - only remove very pure white backgrounds\n      for (let i = 0; i < data.length; i += 4) {\n        const r = data[i];\n        const g = data[i + 1];\n        const b = data[i + 2];\n\n        // Only remove very white pixels (more conservative for watches)\n        const threshold = productType === 'watch' ? 252 : 248;\n        if (r > threshold && g > threshold && b > threshold &&\n            Math.abs(r - g) < 3 && Math.abs(g - b) < 3) {\n          data[i + 3] = 0; // Make transparent\n        }\n      }\n\n      ctx.putImageData(imageData, 0, 0);\n      imgElement.src = canvas.toDataURL('image/png');\n\n      console.log(`Fallback background removal completed for ${productType}`);\n    } catch (error) {\n      console.error('Basic background removal failed:', error);\n    }\n  };\n\n  // Hand detection logic from tryon.js\n  const detectHandOrientation = (imageData) => {\n    // Simple heuristic for demo purposes - can be enhanced with ML models\n    return Math.random() > 0.5;\n  };\n\n  // Initialize camera\n  const initCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment',\n          width: { ideal: 1920 },\n          height: { ideal: 1080 }\n        }\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n    } catch (err) {\n      console.error(\"Error accessing camera:\", err);\n      // Demo mode fallback\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = \"sample-hand.jpg\";\n        capturedImageRef.current.style.display = \"block\";\n      }\n      console.log(\"Camera not available - demo mode\");\n      setIsCaptured(true);\n      setShowProductSelection(true);\n    }\n  };\n\n  // Capture current frame\n  const captureFrame = () => {\n    if (!videoRef.current || !canvasRef.current) return null;\n\n    const canvas = canvasRef.current;\n    const video = videoRef.current;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    const ctx = canvas.getContext('2d');\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n    return canvas.toDataURL('image/png');\n  };\n\n\n\n  // Parse URL parameters on component mount\n  useEffect(() => {\n    const parseUrlParams = () => {\n      const urlSearchParams = new URLSearchParams(window.location.search);\n      const params = {\n        image: urlSearchParams.get('image'),\n        client: urlSearchParams.get('client'),\n        size: urlSearchParams.get('size'),\n        type: urlSearchParams.get('type')\n      };\n\n      console.log('Parsed URL parameters:', params);\n      setUrlParams(params);\n\n      // If image URL is provided, set it as selected product and skip product selection\n      if (params.image) {\n        try {\n          // Validate the image URL\n          const imageUrl = new URL(params.image);\n\n          const productType = params.type || 'watches';\n          const defaultSize = productType === 'bracelets' ? 15 : 42;\n          const actualSize = parseInt(params.size) || defaultSize;\n\n          setSelectedProduct({\n            name: \"Custom Product\",\n            path: params.image,\n            caseDiameter: productType === 'watches' ? actualSize : null,\n            braceletWidth: productType === 'bracelets' ? actualSize : null,\n            caseThickness: productType === 'watches' ? 12 : null,\n            totalWidth: actualSize,\n            totalHeight: productType === 'watches' ? actualSize * 1.15 : actualSize * 3,\n            dialDiameter: productType === 'watches' ? actualSize * 0.75 : null,\n            type: productType,\n            clientId: params.client\n          });\n          setShowProductSelection(false);\n\n          console.log('Product loaded from URL:', {\n            image: params.image,\n            client: params.client,\n            size: params.size\n          });\n        } catch (error) {\n          console.error('Invalid image URL provided:', params.image);\n          // Show product selection if URL is invalid\n          setShowProductSelection(true);\n        }\n      }\n    };\n\n    parseUrlParams();\n  }, []);\n\n  // Start analytics session when URL parameters are loaded\n  useEffect(() => {\n    if (urlParams.client && urlParams.image) {\n      console.log('Starting analytics session with params:', urlParams);\n      startAnalyticsSession();\n\n      // Initialize enhanced tracking\n      const cleanup = initializeEnhancedTracking();\n\n      // Mark try-on as initiated\n      setConversionFunnel(prev => ({\n        ...prev,\n        initiatedTryOn: true\n      }));\n\n      // End session when user leaves the page\n      const handleBeforeUnload = () => {\n        endAnalyticsSession('abandoned');\n      };\n\n      const handleVisibilityChange = () => {\n        if (document.visibilityState === 'hidden') {\n          endAnalyticsSession('abandoned');\n        }\n      };\n\n      window.addEventListener('beforeunload', handleBeforeUnload);\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      return () => {\n        cleanup();\n        window.removeEventListener('beforeunload', handleBeforeUnload);\n        document.removeEventListener('visibilitychange', handleVisibilityChange);\n        endAnalyticsSession('abandoned');\n      };\n    } else {\n      console.log('Missing required parameters for analytics:', {\n        hasClient: !!urlParams.client,\n        hasImage: !!urlParams.image\n      });\n    }\n  }, [urlParams]);\n\n  // Detect mobile device and set viewport height for Chrome mobile\n  useEffect(() => {\n    const checkDevice = () => {\n      const isMobileDevice = window.innerWidth <= 768;\n      setIsMobile(isMobileDevice);\n      setIsDesktop(!isMobileDevice);\n    };\n\n    // Fix viewport height for Chrome mobile\n    const setVH = () => {\n      const vh = window.innerHeight * 0.01;\n      document.documentElement.style.setProperty('--vh', `${vh}px`);\n    };\n\n    checkDevice();\n    setVH();\n\n    window.addEventListener('resize', () => {\n      checkDevice();\n      setVH();\n    });\n\n    window.addEventListener('orientationchange', () => {\n      setTimeout(() => {\n        setVH();\n      }, 100);\n    });\n\n    return () => {\n      window.removeEventListener('resize', checkDevice);\n      window.removeEventListener('orientationchange', setVH);\n    };\n  }, []);\n\n  // Initialize camera when component mounts\n  useEffect(() => {\n    if (typeof window !== 'undefined' && navigator.mediaDevices) {\n      initCamera();\n    }\n    // Show disclaimer popup when component first loads\n    setShowDisclaimerPopup(true);\n  }, []);\n\n  // Realistic sizing configuration\n  // Average adult wrist circumference: 165mm (men), 155mm (women)\n  // Average wrist width when viewed from top: ~55-65mm\n  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)\n  // This means the circle represents approximately 60mm in real life\n\n  // Universal wrist size configuration\n  const DEFAULT_WRIST_SIZE = 50; // mm - ideal wrist width from top view\n  const MIN_WRIST_SIZE = 35; // mm - minimum wrist width\n  const MAX_WRIST_SIZE = 65; // mm - maximum wrist width\n  const ASSUMED_DIAL_SIZE = 42; // mm - assumed real dial size for initial scaling\n\n  // Default wrist sizes by gender (top view width in mm)\n  const DEFAULT_WRIST_SIZES = {\n    men: 50    // mm - average men's wrist width from top view\n  };\n\n  // Add wrist size adjustment constant\n  const WRIST_SIZE_OFFSET = 10; // mm - subtract this from input wrist size for correct fitting\n  const MIN_ADJUSTED_WRIST_SIZE = 37; // Minimum adjusted wrist size before scaling stops\n\n  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units\n  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width\n  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height\n\n  // Size configuration - Increased for better visibility\n  const WATCH_WIDTH = 25; // percentage of container width\n  const BRACELET_WIDTH = 15; // percentage of container width\n  const WATCH_HEIGHT = 38; // percentage of container height\n  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility\n\n  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch\n  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch\n  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet\n  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet\n  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale\n\n  // Modify calculateWatchDimensions to use adjusted wrist size\n  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {\n    // Get the default wrist size for the current gender\n    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n\n    // Adjust the wrist size by subtracting the offset, but don't go below MIN_ADJUSTED_WRIST_SIZE\n    const adjustedWristSize = Math.max(userWristSize - WRIST_SIZE_OFFSET, MIN_ADJUSTED_WRIST_SIZE);\n\n    // Calculate INVERSE scaling factor - smaller wrist = larger watch, larger wrist = smaller watch\n    const inverseWristSizeRatio = defaultWristSize / adjustedWristSize;\n\n    // Calculate the scale factor from real world to SVG coordinates using adjusted wrist size\n    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / adjustedWristSize;\n\n    // Convert watch dimensions to SVG units\n    const watchWidthSvg = watch.totalWidth * mmToSvgScale;\n    const watchHeightSvg = watch.totalHeight * mmToSvgScale;\n    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;\n\n    // Convert SVG units to container percentages\n    const watchWidthPercent = (watchWidthSvg / SVG_VIEWBOX_WIDTH) * 100;\n    const watchHeightPercent = (watchHeightSvg / SVG_VIEWBOX_HEIGHT) * 100;\n    const dialDiameterPercent = (dialDiameterSvg / SVG_VIEWBOX_WIDTH) * 100;\n\n    // Calculate positioning - watches should be positioned on the wrist circle\n    const positionX = 50; // Center horizontally (400/800 = 50%)\n    const positionY = 50; // Center vertically (300/600 = 50%)\n\n    return {\n      width: Math.max(watchWidthPercent, 8), // Minimum 8% for visibility\n      height: Math.max(watchHeightPercent, 10), // Minimum 10% for visibility\n      dialDiameter: dialDiameterPercent,\n      positionX,\n      positionY,\n      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * inverseWristSizeRatio,\n      realWidth: watch.totalWidth,\n      realHeight: watch.totalHeight,\n      caseDiameter: watch.caseDiameter,\n      wristSizeRatio: inverseWristSizeRatio,\n      adjustedWristSize\n    };\n  };\n\n  // Calculate watch position based on hand orientation and anatomy\n  const getWatchPosition = (watchData, isRightHand) => {\n    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);\n\n    // Adjust position based on hand orientation\n    // Watches are typically worn on the top of the wrist\n    let adjustedX = baseDimensions.positionX;\n    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist\n\n    // For right hand, watch might be positioned slightly differently\n    if (isRightHand) {\n      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand\n    }\n\n    // Calculate scale to match SVG shape height\n    const svgHeight = 300; // Height of the wrist/forearm area in SVG\n    const watchHeight = watchData.totalHeight;\n    const scaleToFitHeight = svgHeight / watchHeight;\n\n    // Adjust for different watch types\n    switch (watchData.type) {\n      case 'smartwatch':\n        // Smart watches are often worn higher on the wrist\n        adjustedY -= 1;\n        break;\n      case 'luxury':\n        // Luxury watches might be positioned more precisely\n        adjustedY -= 0.5;\n        break;\n      case 'sport':\n        // Sport watches might be worn slightly looser\n        adjustedY += 0.5;\n        break;\n      default:\n        break;\n    }\n\n    return {\n      ...baseDimensions,\n      positionX: adjustedX,\n      positionY: adjustedY,\n      scale: Math.min(baseDimensions.scale, scaleToFitHeight) // Ensure watch doesn't exceed SVG height\n    };\n  };\n\n  // No static product data - products come from URL parameters or client integration\n\n  // Enhanced tracking functions\n  const initializeEnhancedTracking = () => {\n    // Mouse movement tracking\n    let mouseDistance = 0;\n    let lastMousePos = { x: 0, y: 0 };\n\n    const handleMouseMove = (e) => {\n      const distance = Math.sqrt(\n        Math.pow(e.clientX - lastMousePos.x, 2) +\n        Math.pow(e.clientY - lastMousePos.y, 2)\n      );\n      mouseDistance += distance;\n      lastMousePos = { x: e.clientX, y: e.clientY };\n\n      setBehaviorMetrics(prev => ({\n        ...prev,\n        mouseMovements: mouseDistance\n      }));\n    };\n\n    // Scroll depth tracking\n    const handleScroll = () => {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      const docHeight = document.documentElement.scrollHeight - window.innerHeight;\n      const scrollPercent = Math.round((scrollTop / docHeight) * 100);\n\n      setBehaviorMetrics(prev => ({\n        ...prev,\n        scrollDepth: Math.max(prev.scrollDepth, scrollPercent)\n      }));\n\n      trackInteraction('scroll', { depth: scrollPercent, position: scrollTop });\n    };\n\n    // Focus/blur tracking for attention metrics\n    let focusStartTime = Date.now();\n    let isPageFocused = true;\n\n    const handleFocus = () => {\n      if (!isPageFocused) {\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          attentionMetrics: {\n            ...prev.attentionMetrics,\n            returnEvents: prev.attentionMetrics.returnEvents + 1\n          }\n        }));\n        focusStartTime = Date.now();\n        isPageFocused = true;\n      }\n    };\n\n    const handleBlur = () => {\n      if (isPageFocused) {\n        const focusTime = Date.now() - focusStartTime;\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          attentionMetrics: {\n            ...prev.attentionMetrics,\n            focusTime: prev.attentionMetrics.focusTime + focusTime,\n            blurEvents: prev.attentionMetrics.blurEvents + 1\n          }\n        }));\n        isPageFocused = false;\n      }\n    };\n\n    // Exit intent detection\n    const handleMouseLeave = (e) => {\n      if (e.clientY <= 0) {\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          exitIntent: {\n            detected: true,\n            timestamp: new Date(),\n            beforeConversion: !conversionFunnel.completedPurchase\n          }\n        }));\n        trackInteraction('exit_intent', { timestamp: new Date() });\n      }\n    };\n\n    // Performance monitoring\n    const monitorPerformance = () => {\n      if ('memory' in performance) {\n        const memory = performance.memory;\n        setPerformanceMetrics(prev => ({\n          ...prev,\n          memoryUsage: {\n            used: memory.usedJSHeapSize,\n            total: memory.totalJSHeapSize\n          }\n        }));\n      }\n\n      // Network information\n      if ('connection' in navigator) {\n        const connection = navigator.connection;\n        setPerformanceMetrics(prev => ({\n          ...prev,\n          networkMetrics: {\n            connectionType: connection.type,\n            downlink: connection.downlink,\n            rtt: connection.rtt,\n            effectiveType: connection.effectiveType\n          }\n        }));\n      }\n    };\n\n    // Add event listeners\n    document.addEventListener('mousemove', handleMouseMove);\n    window.addEventListener('scroll', handleScroll);\n    window.addEventListener('focus', handleFocus);\n    window.addEventListener('blur', handleBlur);\n    document.addEventListener('mouseleave', handleMouseLeave);\n\n    // Start performance monitoring\n    monitorPerformance();\n    const performanceInterval = setInterval(monitorPerformance, 5000);\n\n    // Cleanup function\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      window.removeEventListener('scroll', handleScroll);\n      window.removeEventListener('focus', handleFocus);\n      window.removeEventListener('blur', handleBlur);\n      document.removeEventListener('mouseleave', handleMouseLeave);\n      clearInterval(performanceInterval);\n    };\n  };\n\n  // Get user's IP address\n  const getUserIP = async () => {\n    try {\n      const response = await fetch('https://api.ipify.org?format=json');\n      const data = await response.json();\n      return data.ip;\n    } catch (error) {\n      console.warn('Could not get user IP:', error);\n      return null;\n    }\n  };\n\n  // Analytics tracking functions\n  const startAnalyticsSession = async () => {\n    if (!urlParams.client || !urlParams.image) {\n      console.warn('No client ID or image provided for analytics');\n      return;\n    }\n\n    try {\n      const pageLoadStartTime = performance.now();\n      const userIP = await getUserIP();\n\n      const sessionData = {\n        clientId: urlParams.client,\n        productId: urlParams.image,\n        productName: selectedProduct?.name || 'Custom Product',\n        productCategory: urlParams.type || 'watches',\n        device: {\n          type: window.innerWidth <= 768 ? 'mobile' : window.innerWidth <= 1024 ? 'tablet' : 'desktop',\n          screenResolution: `${window.screen.width}x${window.screen.height}`,\n          os: navigator.platform,\n          browser: navigator.userAgent.split(' ').pop(),\n          userAgent: navigator.userAgent\n        },\n        location: {\n          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n          referrer: document.referrer,\n          userIP: userIP\n        },\n        behaviorMetrics: {\n          timeToFirstInteraction: null,\n          cameraInitSuccess: false,\n          handDetectionSuccess: false,\n          backgroundRemovalSuccess: false,\n          productSwitches: 0,\n          productViewTimes: [{\n            productId: urlParams.image,\n            duration: 0\n          }],\n          ...behaviorMetrics\n        },\n        performanceMetrics: {\n          pageLoadTime: null,\n          apiResponseTimes: [],\n          errors: [],\n          ...performanceMetrics\n        },\n        qualityMetrics,\n        conversionFunnel\n      };\n\n      console.log('Sending analytics session data:', sessionData);\n\n      const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n      const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session`;\n\n      const response = await fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(sessionData),\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setSessionId(result.sessionId);\n        setSessionStartTime(new Date());\n        \n        // Record page load time\n        const pageLoadTime = performance.now() - pageLoadStartTime;\n        trackPerformanceMetric('pageLoadTime', pageLoadTime);\n        \n        console.log('Analytics session started:', result.sessionId);\n      } else {\n        const errorData = await response.json();\n        console.error('Failed to start analytics session:', errorData);\n        trackError('session_start', errorData.message);\n      }\n    } catch (error) {\n      console.error('Failed to start analytics session:', error);\n      trackError('session_start', error.message);\n    }\n  };\n\n  const trackInteraction = (type, data = {}) => {\n    const interaction = {\n      type,\n      timestamp: new Date(),\n      data\n    };\n\n    // Update behavior metrics based on interaction type\n    if (type === 'camera_init') {\n      updateBehaviorMetric('cameraInitSuccess', true);\n    } else if (type === 'hand_detection') {\n      updateBehaviorMetric('handDetectionSuccess', true);\n    } else if (type === 'background_removal') {\n      updateBehaviorMetric('backgroundRemovalSuccess', true);\n    } else if (type === 'product_switch') {\n      updateBehaviorMetric('productSwitches', (prev) => prev + 1);\n    }\n\n    // Enhanced interaction data\n    interaction.position = data.position || { x: 0, y: 0 };\n    interaction.element = data.element || '';\n    interaction.duration = data.duration || 0;\n    interaction.intensity = data.intensity || 0;\n    interaction.sequence = interactions.length + 1;\n    interaction.context = data.context || '';\n\n    // Update feature usage metrics using the new updateBehaviorMetric function\n    if (type === 'screenshot') {\n      updateBehaviorMetric('featureUsage.screenshot', (prev) => prev + 1);\n      setConversionFunnel(prev => ({\n        ...prev,\n        completedTryOn: true\n      }));\n    } else if (type === 'share') {\n      updateBehaviorMetric('featureUsage.share', (prev) => prev + 1);\n      setConversionFunnel(prev => ({\n        ...prev,\n        sharedResult: true\n      }));\n    } else if (type === 'zoom') {\n      updateBehaviorMetric('featureUsage.zoom', (prev) => prev + 1);\n    } else if (type === 'size_adjustment') {\n      updateBehaviorMetric('featureUsage.sizeAdjustment', (prev) => prev + 1);\n    } else if (type === 'color_change') {\n      updateBehaviorMetric('featureUsage.colorChange', (prev) => prev + 1);\n    } else if (type === 'product_switch') {\n      updateBehaviorMetric('featureUsage.productRotation', (prev) => prev + 1);\n    } else if (type === 'capture') {\n      updateBehaviorMetric('featureUsage.cameraToggle', (prev) => prev + 1);\n    }\n\n    // Track time to first interaction if not set\n    if (!behaviorMetrics.timeToFirstInteraction && sessionStartTime) {\n      const timeToFirstInteraction = (new Date() - sessionStartTime) / 1000;\n      updateBehaviorMetric('timeToFirstInteraction', timeToFirstInteraction);\n    }\n\n    // Calculate engagement score\n    const engagementScore = Math.min(100,\n      (interactions.length * 5) +\n      (behaviorMetrics.featureUsage.screenshot * 10) +\n      (behaviorMetrics.featureUsage.share * 15) +\n      (behaviorMetrics.scrollDepth * 0.2) +\n      (behaviorMetrics.mouseMovements * 0.001)\n    );\n\n    setBehaviorMetrics(prev => ({\n      ...prev,\n      engagementScore: Math.round(engagementScore)\n    }));\n\n    setInteractions(prev => [...prev, interaction]);\n    console.log('Enhanced interaction tracked:', interaction);\n  };\n\n  const trackPerformanceMetric = (metric, value) => {\n    const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n    const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}/performance`;\n\n    fetch(apiUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        metric,\n        value,\n        timestamp: new Date()\n      }),\n    }).catch(error => {\n      console.error('Failed to track performance metric:', error);\n    });\n  };\n\n  const trackError = (type, message) => {\n    const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n    const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}/error`;\n\n    fetch(apiUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        type,\n        message,\n        timestamp: new Date()\n      }),\n    }).catch(error => {\n      console.error('Failed to track error:', error);\n    });\n  };\n\n  const updateBehaviorMetric = (metric, value) => {\n    // Update local state\n    setBehaviorMetrics(prev => {\n      const newMetrics = { ...prev };\n\n      // Handle nested properties like 'featureUsage.screenshot'\n      if (metric.includes('.')) {\n        const [parent, child] = metric.split('.');\n        if (newMetrics[parent]) {\n          newMetrics[parent] = { ...newMetrics[parent], [child]: value };\n        }\n      } else {\n        newMetrics[metric] = typeof value === 'function' ? value(prev[metric]) : value;\n      }\n\n      return newMetrics;\n    });\n\n    // Send to backend if session exists\n    if (sessionId) {\n      const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n      const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}/behavior`;\n\n      fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          metric,\n          value: typeof value === 'function' ? value(behaviorMetrics[metric] || 0) : value,\n          timestamp: new Date()\n        }),\n      }).catch(error => {\n        console.error('Failed to update behavior metric:', error);\n      });\n    }\n  };\n\n  const endAnalyticsSession = async (outcome = 'abandoned') => {\n    if (!sessionId) return;\n\n    try {\n      const endTime = new Date();\n      const duration = sessionStartTime ? Math.floor((endTime - sessionStartTime) / 1000) : 0;\n\n      const updateData = {\n        endTime,\n        duration,\n        outcome,\n        interactions,\n        behaviorMetrics,\n        performanceMetrics,\n        qualityMetrics,\n        conversionFunnel\n      };\n\n      const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n      const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}`;\n\n      const response = await fetch(apiUrl, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(updateData),\n      });\n\n      if (response.ok) {\n        console.log('Analytics session ended:', outcome, 'Duration:', duration, 'seconds');\n      } else {\n        const errorData = await response.json();\n        console.error('Failed to end analytics session:', errorData);\n      }\n    } catch (error) {\n      console.error('Failed to end analytics session:', error);\n    }\n  };\n\n\n\n\n\n  // Detect if arm and wrist are within the SVG guide area\n  const detectHandInPosition = () => {\n    if (!videoRef.current || !canvasRef.current) return false;\n\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw current video frame\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Get the video container dimensions to calculate the guide area\n    const videoContainer = video.parentElement;\n    const containerRect = videoContainer.getBoundingClientRect();\n\n    // Calculate the actual video display area (accounting for object-fit: cover)\n    const videoAspect = video.videoWidth / video.videoHeight;\n    const containerAspect = containerRect.width / containerRect.height;\n\n    let displayWidth, displayHeight, offsetX, offsetY;\n\n    if (videoAspect > containerAspect) {\n      // Video is wider - height fills container, width is cropped\n      displayHeight = containerRect.height;\n      displayWidth = displayHeight * videoAspect;\n      offsetX = (displayWidth - containerRect.width) / 2;\n      offsetY = 0;\n    } else {\n      // Video is taller - width fills container, height is cropped\n      displayWidth = containerRect.width;\n      displayHeight = displayWidth / videoAspect;\n      offsetX = 0;\n      offsetY = (displayHeight - containerRect.height) / 2;\n    }\n\n    // Calculate the guide areas in canvas coordinates\n    // SVG viewBox is 800x600\n    // Main rectangle: x=\"320\" y=\"150\" width=\"160\" height=\"300\" (wrist/forearm area)\n    // Circle: cx=\"400\" cy=\"300\" r=\"60\" (hand area)\n\n    const scaleX = canvas.width / displayWidth;\n    const scaleY = canvas.height / displayHeight;\n\n    // Wrist/forearm area (rectangle)\n    const rectX = Math.max(0, ((320 / 800) * displayWidth - offsetX) * scaleX);\n    const rectY = Math.max(0, ((150 / 600) * displayHeight - offsetY) * scaleY);\n    const rectWidth = Math.min(canvas.width - rectX, ((160 / 800) * displayWidth) * scaleX);\n    const rectHeight = Math.min(canvas.height - rectY, ((300 / 600) * displayHeight) * scaleY);\n\n    // Hand area (circle)\n    const circleX = Math.max(0, ((340 / 800) * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds\n    const circleY = Math.max(0, ((240 / 600) * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds\n    const circleWidth = Math.min(canvas.width - circleX, ((120 / 800) * displayWidth) * scaleX); // Circle diameter\n    const circleHeight = Math.min(canvas.height - circleY, ((120 / 600) * displayHeight) * scaleY); // Circle diameter\n\n    try {\n      // Check wrist/forearm area (rectangle)\n      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);\n      const rectData = rectImageData.data;\n\n      // Check hand area (circle approximation)\n      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);\n      const circleData = circleImageData.data;\n\n      let rectSkinPixels = 0;\n      let rectTotalPixels = 0;\n      let circleSkinPixels = 0;\n      let circleTotalPixels = 0;\n\n      // Enhanced skin tone detection\n      const isSkinTone = (r, g, b) => {\n        // Multiple skin tone ranges to cover different skin colors\n        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;\n        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;\n        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;\n        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;\n\n        return condition1 || condition2 || condition3 || condition4;\n      };\n\n      // Analyze rectangle area (wrist/forearm)\n      for (let i = 0; i < rectData.length; i += 4) {\n        const r = rectData[i];\n        const g = rectData[i + 1];\n        const b = rectData[i + 2];\n\n        if (isSkinTone(r, g, b)) {\n          rectSkinPixels++;\n        }\n        rectTotalPixels++;\n      }\n\n      // Analyze circle area (hand)\n      for (let i = 0; i < circleData.length; i += 4) {\n        const r = circleData[i];\n        const g = circleData[i + 1];\n        const b = circleData[i + 2];\n\n        if (isSkinTone(r, g, b)) {\n          circleSkinPixels++;\n        }\n        circleTotalPixels++;\n      }\n\n      // Calculate skin ratios\n      const rectSkinRatio = rectSkinPixels / rectTotalPixels;\n      const circleSkinRatio = circleSkinPixels / circleTotalPixels;\n\n      // Both wrist/forearm AND hand areas must have sufficient skin tone presence\n      // Wrist area needs at least 20% skin tone (forearm/wrist)\n      // Hand area needs at least 25% skin tone (hand/fingers)\n      const wristInPosition = rectSkinRatio > 0.20;\n      const handInPosition = circleSkinRatio > 0.25;\n\n      return wristInPosition && handInPosition;\n\n    } catch (error) {\n      console.warn('Hand detection error:', error);\n      return false;\n    }\n  };\n\n  // Apply product to watch position with dynamic scaling\n  const applyProductToWatchPosition = (productPath, productType) => {\n    // Clear any existing product first\n    setSelectedProduct(null);\n\n    // Find the watch data for dial size\n    const watchData = watches.find(w => w.path === productPath);\n\n    // Small delay to ensure the old product is removed before adding new one\n    setTimeout(() => {\n      setSelectedProduct({\n        path: productPath,\n        dialSize: watchData?.dialSize || 40, // Default to 40mm if not found\n        dimensions: watchData // Pass full watch dimensions for scaling\n      });\n    }, 50);\n  };\n\n  // Screenshot functionality with tracking\n  const takeScreenshot = () => {\n    if (!capturedImageRef.current) return;\n\n    try {\n      // Create a canvas to combine the captured image with the product overlay\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n\n      // Set canvas size\n      canvas.width = capturedImageRef.current.naturalWidth || 800;\n      canvas.height = capturedImageRef.current.naturalHeight || 600;\n\n      // Draw the captured image\n      ctx.drawImage(capturedImageRef.current, 0, 0, canvas.width, canvas.height);\n\n      // Convert to blob and download\n      canvas.toBlob((blob) => {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `tryon-${Date.now()}.png`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n\n        // Track screenshot\n        trackInteraction('screenshot', {\n          timestamp: new Date(),\n          productId: urlParams.image,\n          sessionDuration: sessionStartTime ? (new Date() - sessionStartTime) / 1000 : 0\n        });\n\n        // Mark as completed try-on\n        setConversionFunnel(prev => ({\n          ...prev,\n          completedTryOn: true\n        }));\n      }, 'image/png');\n    } catch (error) {\n      console.error('Screenshot failed:', error);\n      trackError('screenshot_failed', error.message);\n    }\n  };\n\n  // Zoom functionality with tracking\n  const handleZoom = (zoomLevel) => {\n    trackInteraction('zoom', {\n      zoomLevel,\n      timestamp: new Date(),\n      element: 'product_overlay'\n    });\n  };\n\n  // Share functionality with tracking\n  const handleShare = async () => {\n    try {\n      if (navigator.share) {\n        await navigator.share({\n          title: 'Check out my virtual try-on!',\n          text: 'I tried on this product virtually',\n          url: window.location.href\n        });\n\n        trackInteraction('share', {\n          method: 'native_share',\n          timestamp: new Date()\n        });\n\n        setConversionFunnel(prev => ({\n          ...prev,\n          sharedResult: true\n        }));\n      } else {\n        // Fallback to copying URL\n        await navigator.clipboard.writeText(window.location.href);\n        alert('Link copied to clipboard!');\n\n        trackInteraction('share', {\n          method: 'copy_link',\n          timestamp: new Date()\n        });\n      }\n    } catch (error) {\n      console.error('Share failed:', error);\n      trackError('share_failed', error.message);\n    }\n  };\n\n  // Handle Try On Model button click\n  const handleTryOnModel = () => {\n    if (!isCaptured) {\n      // Load the model hand image from public folder\n      const modelImagePath = '/imgs/hand/hand.png'; // Using the hand.jpg from public folder\n\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = modelImagePath;\n        capturedImageRef.current.style.display = 'block';\n\n        // Wait for image to load, then detect wrist and apply try-on logic\n        capturedImageRef.current.onload = () => {\n          // Simulate wrist detection on the model image\n          detectWristOnModelImage();\n        };\n      }\n\n      setIsCaptured(true);\n      setIsUsingModelImage(true);\n      // Only show product selection if no product is provided via URL\n      if (!urlParams.image) {\n        setShowProductSelection(true);\n      }\n      setShowHandGuide(false);\n\n      // Track try-on model interaction\n      trackInteraction('capture', {\n        method: 'model_tryon',\n        hasProduct: !!urlParams.image,\n        timestamp: new Date()\n      });\n\n      // Set hand orientation for model (assume left hand for consistency)\n      setIsRightHand(false);\n    }\n  };\n\n  // Detect wrist position on model image and apply scaling\n  const detectWristOnModelImage = () => {\n    if (!capturedImageRef.current) return;\n\n    try {\n      // Create a canvas to analyze the model image\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = capturedImageRef.current;\n\n      canvas.width = img.naturalWidth || 800;\n      canvas.height = img.naturalHeight || 600;\n      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n\n      // For model image with closed palm, position the watch/bracelet on the palm area\n      // Since it's a closed hand, we need to detect the palm center rather than wrist\n      const palmPosition = detectPalmPositionOnModel(canvas);\n\n      if (palmPosition) {\n        console.log('Palm position detected on model image:', palmPosition);\n\n        // Store the palm position for product placement\n        window.modelPalmPosition = palmPosition;\n\n        // Mark behavior metrics for successful hand detection\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          handDetectionSuccess: true,\n          palmDetected: true\n        }));\n\n        // Track successful palm detection\n        trackInteraction('hand_detection', {\n          method: 'model_image_palm',\n          success: true,\n          palmPosition: palmPosition,\n          timestamp: new Date()\n        });\n      } else {\n        console.warn('Could not detect palm position on model image');\n      }\n\n    } catch (error) {\n      console.warn('Model image palm detection error:', error);\n      trackError('model_palm_detection', error.message);\n    }\n  };\n\n  // Detect palm position on closed hand model image\n  const detectPalmPositionOnModel = (canvas) => {\n    try {\n      const ctx = canvas.getContext('2d');\n      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n      const data = imageData.data;\n\n      // For a closed hand model, we need to find the wrist area where the watch/bracelet should go\n      // The wrist is typically at the bottom of the hand, where the arm connects\n\n      // Scan from bottom up to find the wrist area (narrowest part of the arm)\n      let wristY = null;\n      let wristX = canvas.width * 0.5; // Start from center\n      let minWidth = canvas.width;\n\n      // Scan the bottom third of the image to find the wrist\n      const startY = Math.floor(canvas.height * 0.7); // Start from 70% down\n      const endY = canvas.height - 20; // Stop 20px from bottom\n\n      for (let y = startY; y < endY; y += 2) {\n        let leftEdge = null;\n        let rightEdge = null;\n\n        // Scan horizontally to find the edges of the arm/wrist\n        for (let x = 0; x < canvas.width; x++) {\n          const pixelIndex = (y * canvas.width + x) * 4;\n          const r = data[pixelIndex];\n          const g = data[pixelIndex + 1];\n          const b = data[pixelIndex + 2];\n          const alpha = data[pixelIndex + 3];\n\n          // Check if pixel is part of the hand/arm (not background)\n          // Adjust threshold based on the hand image characteristics\n          const isHandPixel = alpha > 100 && (r < 200 || g < 200 || b < 200);\n\n          if (isHandPixel && leftEdge === null) {\n            leftEdge = x;\n          }\n          if (isHandPixel) {\n            rightEdge = x;\n          }\n        }\n\n        // If we found both edges, check if this is the narrowest point (wrist)\n        if (leftEdge !== null && rightEdge !== null) {\n          const armWidth = rightEdge - leftEdge;\n\n          if (armWidth < minWidth && armWidth > 40) { // Reasonable wrist width\n            minWidth = armWidth;\n            wristY = y;\n            wristX = leftEdge + armWidth / 2;\n          }\n        }\n      }\n\n      // If we couldn't detect the wrist automatically, use default position\n      if (!wristY) {\n        wristY = canvas.height * 0.65; // 85% down from top (moved down a bit)\n        wristX = canvas.width * 0.5; // Center horizontally\n        console.log('Using default wrist position for closed hand model');\n      }\n\n      // Calculate the wrist area for product placement\n      // Make the watch bigger for model hand since it's larger than SVG\n      const wristBounds = {\n        x: wristX - 80, // Increased wrist area width for bigger watch\n        y: wristY - 40, // Increased wrist area height for bigger watch\n        width: 160, // Increased from 120 to make watch bigger\n        height: 60, // Increased from 60 to make watch bigger\n        centerX: wristX,\n        centerY: wristY + 10, // Move center down a bit\n        confidence: wristY ? 0.9 : 0.7,\n        isModelHand: true // Flag to indicate this is model hand for scaling adjustments\n      };\n\n      console.log('Detected wrist area for closed hand model:', wristBounds);\n\n      return wristBounds;\n\n    } catch (error) {\n      console.error('Error detecting wrist position on closed hand:', error);\n      // Return default wrist position with bigger size for model hand\n      return {\n        x: canvas.width * 0.5 - 80,\n        y: canvas.height * 0.85 - 40,\n        width: 160,\n        height: 80,\n        centerX: canvas.width * 0.5,\n        centerY: canvas.height * 0.85 + 10,\n        confidence: 0.7,\n        isModelHand: true\n      };\n    }\n  };\n\n  // Handle capture button click\n  const handleCapture = () => {\n    if (!isCaptured) {\n      const capturedDataUrl = captureFrame();\n      if (capturedImageRef.current && capturedDataUrl) {\n        capturedImageRef.current.src = capturedDataUrl;\n        capturedImageRef.current.style.display = 'block';\n      }\n      setIsCaptured(true);\n      setIsUsingModelImage(false);\n      // Only show product selection if no product is provided via URL\n      if (!urlParams.image) {\n        setShowProductSelection(true);\n      }\n      setShowHandGuide(false);\n\n      // Track capture interaction\n      trackInteraction('capture', {\n        method: 'manual_capture',\n        hasProduct: !!urlParams.image,\n        timestamp: new Date()\n      });\n\n      // Detect hand orientation\n      if (canvasRef.current && videoRef.current) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        canvas.width = videoRef.current.videoWidth;\n        canvas.height = videoRef.current.videoHeight;\n        ctx.drawImage(videoRef.current, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        setIsRightHand(detectHandOrientation(imageData));\n      }\n    } else {\n      // Only show product selection if no product is provided via URL\n      if (!urlParams.image) {\n        setShowProductSelection(true);\n      }\n    }\n  };\n\n  // Handle back button click\n  const handleBack = () => {\n    if (capturedImageRef.current) {\n      capturedImageRef.current.style.display = 'none';\n    }\n    setIsCaptured(false);\n    setSelectedProduct(null);\n    setIsRightHand(false);\n    setShowProductSelection(false);\n    setShowWristSizeModal(false);\n    setShowHandGuide(true);\n    setIsUsingModelImage(false); // Reset model image state\n  };\n\n  // Handle gender selection\n  const handleGenderChange = (gender) => {\n    setUserGender('men'); // Single gender role\n    setUserWristSize(50); // Set initial size for men\n    trackInteraction('size_adjustment', {\n      type: 'gender_change',\n      gender: 'men',\n      newWristSize: 50\n    });\n  };\n\n  // Handle wrist size change\n  const handleWristSizeChange = (size) => {\n    setUserWristSize(size);\n    trackInteraction('size_adjustment', {\n      type: 'wrist_size_change',\n      size\n    });\n  };\n\n  // Handle tab change\n  const handleTabChange = (tabName) => {\n    setActiveTab(tabName);\n  };\n\n  // Handle autocapture switch toggle\n  const handleAutoCaptureToggle = () => {\n    const newState = !isAutoCaptureEnabled;\n    setIsAutoCaptureEnabled(newState);\n\n    // Reset states when turning off\n    if (!newState) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      setIsHandInPosition(false);\n    }\n  };\n\n  // Reset autocapture when going back\n  const handleBackWithReset = () => {\n    setIsAutoCaptureEnabled(false);\n    setIsCountdownActive(false);\n    setCountdown(0);\n    setIsHandInPosition(false);\n    setShowWristSizeModal(false);\n    setUserWristSize(50); // Default to men's size\n    setIsUsingModelImage(false); // Reset model image state\n    // Go back to wherever user came from instead of just resetting\n    onBackToHome();\n  };\n\n  // Get current products based on active tab\n  const getCurrentProducts = () => {\n    return activeTab === 'Watches' ? watches : bracelets;\n  };\n\n  // Handle product selection\n  const handleProductSelect = (product) => {\n    applyProductToWatchPosition(product.path, activeTab);\n  };\n\n  // Handle disclaimer popup close\n  const handleDisclaimerClose = () => {\n    setShowDisclaimerPopup(false);\n  };\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n\n\n  // Add touch gesture handlers\n  const handleTouchStart = (e) => {\n    setIsDragging(true);\n    setStartY(e.touches[0].clientY);\n  };\n\n  const handleTouchMove = (e) => {\n    if (!isDragging) return;\n    \n    const currentY = e.touches[0].clientY;\n    const diff = currentY - startY;\n    \n    // Only allow dragging down\n    if (diff > 0) {\n      setPanelPosition(diff);\n    }\n  };\n\n  const handleTouchEnd = () => {\n    if (!isDragging) return;\n    \n    setIsDragging(false);\n    \n    // If dragged more than 100px down, close the panel\n    if (panelPosition > 100) {\n      setShowProductSelection(false);\n    }\n    \n    // Reset position\n    setPanelPosition(0);\n  };\n\n  // Add click outside handler for modals\n  useEffect(() => {\n    const handleClickOutside = (e) => {\n      if (showWristSizeModal && !e.target.closest('.modal-content')) {\n        setShowWristSizeModal(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    document.addEventListener('touchstart', handleClickOutside);\n    \n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('touchstart', handleClickOutside);\n    };\n  }, [showWristSizeModal]);\n\n  // Desktop QR Code Component\n  const DesktopQRCode = () => {\n    // Generate dynamic QR code URL based on URL parameters or default\n    const generateQRValue = () => {\n      const websiteUrl = process.env.REACT_APP_WEBSITE_URL || 'https://viatryon.com';\n      const baseUrl = `${websiteUrl}/tryon`;\n\n      // If we have URL parameters, include them in the QR code\n      if (urlParams.image || urlParams.client || urlParams.size || urlParams.type) {\n        const params = new URLSearchParams();\n        if (urlParams.image) params.append('image', urlParams.image);\n        if (urlParams.client) params.append('client', urlParams.client);\n        if (urlParams.size) params.append('size', urlParams.size);\n        if (urlParams.type) params.append('type', urlParams.type);\n        return `${baseUrl}?${params.toString()}`;\n      }\n\n      // Default QR code for general try-on\n      return baseUrl;\n    };\n\n    const qrValue = generateQRValue();\n\n    return (\n      <div style={styles.desktopContainer}>\n        <div style={styles.qrContainer}>\n          <h2 style={styles.qrTitle}>Scan QR Code to Try On</h2>\n          <p style={styles.qrSubtitle}>\n            {urlParams.image\n              ? \"Scan to try on this specific product on your mobile device\"\n              : \"Open this page on your mobile device to experience the virtual try-on feature\"\n            }\n          </p>\n          <div style={styles.qrWrapper}>\n            <QRCodeSVG\n              value={qrValue}\n              size={256}\n              level=\"H\"\n              bgColor=\"#FFFFFF\"\n              fgColor=\"#2D8C88\"\n            />\n          </div>\n          <p style={styles.qrLink}>{qrValue}</p>\n          {urlParams.client && (\n            <p style={styles.clientInfo}>Client: {urlParams.client}</p>\n          )}\n          <button\n            style={styles.homeBtn}\n            onClick={onBackToHome}\n            aria-label=\"Home\"\n          >\n            ← Back to Home\n          </button>\n        </div>\n      </div>\n    );\n  };\n\n  // Return desktop view if not on mobile\n  if (isDesktop) {\n    return <DesktopQRCode />;\n  }\n\n  // Update product selection panel JSX\n  return (\n    <div style={styles.container}>\n      {/* Disclaimer Popup */}\n      {showDisclaimerPopup && (\n        <div style={styles.disclaimerOverlay}>\n          <div style={styles.disclaimerPopup}>\n            <div style={styles.disclaimerContent}>\n              <h3 style={styles.disclaimerTitle}>Before You Start</h3>\n              <div style={styles.disclaimerPoints}>\n                <p style={styles.disclaimerPoint}>\n                  Images shown are for demonstration purposes - actual size may vary\n                </p>\n                <p style={styles.disclaimerPoint}>\n                  Position your wrist within the guide lines for better results\n                </p>\n              </div>\n              <button\n                style={styles.disclaimerButton}\n                onClick={handleDisclaimerClose}\n              >\n                Got it\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Clean Branding - Tangiblee style */}\n      <div style={styles.brandingContainerTangiblee}>\n        <img\n          src=\"/imgs/logo-only.png\"\n          alt=\"ViaTryon\"\n          style={styles.brandingLogoTangiblee}\n          onError={(e) => {\n            e.target.style.display = 'none';\n          }}\n        />\n        <div style={styles.brandingTextStacked}>\n          <span style={{\n            ...styles.poweredByText,\n            color: isUsingModelImage ? '#000000' : styles.poweredByText.color\n          }}>Powered by</span>\n          <span style={{\n            ...styles.viatryonText,\n            color: isUsingModelImage ? '#000000' : styles.viatryonText.color\n          }}>ViaTryon</span>\n        </div>\n      </div>\n\n      <div style={styles.cameraContainer}>\n        <video\n          ref={videoRef}\n          style={styles.cameraFeed}\n          autoPlay\n          playsInline\n          muted\n        />\n        <canvas ref={canvasRef} style={{ display: 'none' }} />\n        <img\n          ref={capturedImageRef}\n          style={styles.capturedImage}\n          alt=\"Captured hand\"\n        />\n\n        {/* Clean Auto Capture Toggle */}\n        {!isCaptured && (\n          <div style={styles.autoCaptureToggle}>\n            <span style={styles.autoCaptureLabel}>Auto</span>\n            <label className=\"switch-container\">\n              <input\n                type=\"checkbox\"\n                checked={isAutoCaptureEnabled}\n                onChange={handleAutoCaptureToggle}\n                disabled={isCountdownActive}\n                aria-label=\"Toggle auto capture\"\n              />\n              <span className=\"switch-slider\"></span>\n            </label>\n          </div>\n        )}\n\n        {/* Countdown Display - Only visible during active countdown */}\n        {isCountdownActive && (\n          <div style={styles.countdownDisplay}>\n            <div style={styles.countdownNumber}>{countdown}</div>\n            <div style={styles.countdownText}>Auto capturing...</div>\n          </div>\n        )}\n\n        {/* Simple Instruction Message - Clean and minimal */}\n        {!isCaptured && !isCountdownActive && !isAutoCaptureEnabled && (\n          <div style={styles.instructionContainer}>\n            <div style={styles.instructionText}>\n              Position your wrist within the guides\n            </div>\n          </div>\n        )}\n\n        {/* Status Messages */}\n        {isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && (\n          <div style={styles.statusMessageSmall}>\n            <div style={styles.statusTextSmall}>Position your arm and wrist in the guide area</div>\n            <div style={styles.statusSubtextSmall}>Countdown will start automatically when detected</div>\n          </div>\n        )}\n\n        {isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && (\n          <div style={styles.statusMessage}>\n            <div style={{...styles.statusText, backgroundColor: 'rgba(45, 140, 136, 0.9)'}}>\n              Perfect! Starting countdown...\n            </div>\n          </div>\n        )}\n\n        {/* Hand Guide SVG */}\n        {showHandGuide && (\n          <div\n            style={{\n              ...styles.handGuide,\n              opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,\n              filter: isAutoCaptureEnabled && isHandInPosition\n                ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))'\n                : isAutoCaptureEnabled && !isHandInPosition\n                  ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))'\n                  : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'\n            }}\n            className={isMobile ? 'mobile-hand-guide' : ''}\n            aria-hidden=\"true\"\n          >\n            <svg viewBox=\"0 0 800 600\" xmlns=\"http://www.w3.org/2000/svg\">\n              {/* Wrist guide lines */}\n              <path\n                d=\"M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"5\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n              />\n              <path\n                d=\"M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"5\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n              />\n              {/* Wrist/Forearm area (rectangle) */}\n              <rect\n                x=\"320\"\n                y=\"150\"\n                width=\"160\"\n                height=\"300\"\n                fill={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                opacity={isAutoCaptureEnabled && isHandInPosition ? \"0.25\" : \"0.15\"}\n                rx=\"15\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"2\"\n              />\n              {/* Hand area (circle) */}\n              <circle\n                cx=\"400\"\n                cy=\"300\"\n                r=\"60\"\n                fill={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                opacity={isAutoCaptureEnabled && isHandInPosition ? \"0.3\" : \"0.2\"}\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"2\"\n              />\n              {/* Labels for clarity */}\n              {isAutoCaptureEnabled && (\n                <>\n                  <text x=\"400\" y=\"140\" textAnchor=\"middle\" fill=\"white\" fontSize=\"14\" fontWeight=\"bold\">\n                    WRIST & FOREARM\n                  </text>\n                  <text x=\"400\" y=\"480\" textAnchor=\"middle\" fill=\"white\" fontSize=\"14\" fontWeight=\"bold\">\n                    HAND\n                  </text>\n                </>\n              )}\n            </svg>\n          </div>\n        )}\n\n        {/* Product Display - Only show after capture */}\n        {selectedProduct && isCaptured && (\n          <div style={{\n            ...styles.productPosition,\n            // Adjust positioning for model hand - move down and make bigger\n            top: isUsingModelImage ? '55%' : '50%',\n            transform: isUsingModelImage ? 'translate(-50%, -50%) scale(1.3)' : 'translate(-50%, -50%)',\n            width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\n            height: activeTab === 'Watches'\n              ? (() => {\n                  const defaultWristSize = DEFAULT_WRIST_SIZE;\n                  const isLargeWrist = (userWristSize >= DEFAULT_WRIST_SIZE);\n\n                  if (isLargeWrist) {\n                    // For large wrists, increase height by 40% to allow exceeding SVG height\n                    const sizeIncrease = (userWristSize - DEFAULT_WRIST_SIZE) / DEFAULT_WRIST_SIZE;\n                    return `${WATCH_HEIGHT * (1 + sizeIncrease * 0.9)}%`;\n                  }\n                  return `${WATCH_HEIGHT}%`;\n                })()\n              : `${BRACELET_HEIGHT}%`,\n            // Apply clipping for wrist sizes >= 50mm (men) and >= 45mm (women)\n            clipPath: (() => {\n              const isLargeWrist = (userWristSize >= DEFAULT_WRIST_SIZE);\n              return activeTab === 'Watches' && isLargeWrist\n                ? 'ellipse(220px 60px at 50% 50%)'\n                : activeTab === 'Watches' && userWristSize < DEFAULT_WRIST_SIZE\n                  ? 'ellipse(220px 60px at 50% 50%)'\n                  : 'none';\n            })(),\n            overflow: 'hidden'\n          }}>\n            <div style={{\n              position: 'relative',\n              width: '100%',\n              height: '100%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}>\n              <img\n                src={typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct}\n                alt=\"Selected product\"\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'contain',\n                  transform: activeTab === 'Bracelets'\n                    ? (() => {\n                        // Use exact bracelet fitting logic from tryon.js with enhanced vertical flip\n                        const baseTransform = `rotate(90deg) scale(${BRACELET_HEIGHT / 30})`;\n\n                        // Apply realistic bracelet positioning based on hand detection\n                        // Bracelets need vertical flipping to appear correctly on the wrist\n                        if (isRightHand) {\n                          // For right hand: flip horizontally (like tryon.js) and add vertical flip for realism\n                          return `${baseTransform} scaleX(-1) scaleY(-1)`;\n                        } else {\n                          // For left hand: only vertical flip for proper bracelet orientation\n                          return `${baseTransform} scaleY(-1)`;\n                        }\n                      })()\n                    : (() => {\n                        const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n                        const adjustedWristSize = Math.max(userWristSize - WRIST_SIZE_OFFSET, MIN_ADJUSTED_WRIST_SIZE);\n                        const isLargeWrist = (userGender === 'men' && adjustedWristSize >= 50);\n\n                        if (isLargeWrist) {\n                          // For larger wrists, apply height scaling increase and allow exceeding SVG height\n                          const sizeIncrease = (adjustedWristSize - defaultWristSize) / defaultWristSize;\n                          const heightScale = 1 + (sizeIncrease * 0.4); // 40% height increase\n                          const widthScale = defaultWristSize / adjustedWristSize; // Decrease width as wrist increases\n\n                          return `scale(${(WATCH_HEIGHT / 25) * widthScale}) scaleX(${widthScale}) scaleY(${heightScale})`;\n                        }\n\n                        // For smaller wrists, use the original working logic with SVG height constraint\n                        return `scale(${Math.min(\n                          (WATCH_HEIGHT / 25) * (adjustedWristSize > defaultWristSize\n                            ? defaultWristSize / adjustedWristSize\n                            : defaultWristSize / adjustedWristSize),\n                          300 / (selectedProduct.dimensions?.totalHeight || 47) // Scale to match SVG height\n                        )}) scaleX(${adjustedWristSize > defaultWristSize\n                          ? defaultWristSize / adjustedWristSize\n                          : 1})`;\n                      })(),\n                  filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\n                }}\n                onLoad={(e) => removeBackground(e.target, (urlParams.type === 'bracelets' || activeTab === 'Bracelets') ? 'bracelet' : 'watch')}\n              />\n              {((urlParams.type !== 'bracelets' && activeTab === 'Watches') || (!urlParams.type && activeTab === 'Watches')) && typeof selectedProduct === 'object' && (\n                <div style={{\n                  position: 'absolute',\n                  bottom: '-30px',\n                  left: '50%',\n                  transform: 'translateX(-50%)',\n                  fontSize: '11px',\n                  fontWeight: '600',\n                  color: 'white',\n                  backgroundColor: 'rgba(45, 140, 136, 0.9)',\n                  padding: '3px 8px',\n                  borderRadius: '12px',\n                  whiteSpace: 'nowrap',\n                  pointerEvents: 'none',\n                  boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n                  zIndex: 2\n                }}>\n                  {selectedProduct.dialSize || selectedProduct.caseDiameter || urlParams.size || '42'}mm\n                  {userWristSize !== DEFAULT_WRIST_SIZE && (\n                    <span style={{\n                      fontSize: '10px',\n                      opacity: 0.8,\n                      marginLeft: '4px'\n                    }}>\n                      {(() => {\n                        const wristSizeRatio = DEFAULT_WRIST_SIZE / userWristSize;\n\n                        let scalingPercentage;\n                        if (userWristSize < DEFAULT_WRIST_SIZE) {\n                          // Realistic scaling for smaller wrists\n                          const sizeDifference = DEFAULT_WRIST_SIZE - userWristSize;\n                          const maxSizeDifference = DEFAULT_WRIST_SIZE * 0.25;\n                          const clampedDifference = Math.min(sizeDifference, maxSizeDifference);\n                          const moderateScaleFactor = 1 + (clampedDifference / DEFAULT_WRIST_SIZE) * 0.6;\n                          scalingPercentage = ((moderateScaleFactor - 1) * 100).toFixed(0);\n                        } else {\n                          // Standard scaling for larger wrists\n                          scalingPercentage = ((wristSizeRatio - 1) * 100).toFixed(0);\n                        }\n\n                        // Add debug indicator for large wrists\n                        const debugSuffix = userWristSize >= DEFAULT_WRIST_SIZE ? ' 🔥' : '';\n                        return `(${userWristSize < DEFAULT_WRIST_SIZE ? '+' : ''}${scalingPercentage}%)${debugSuffix}`;\n                      })()}\n                    </span>\n                  )}\n                </div>\n              )}\n              {(urlParams.type === 'bracelets' || activeTab === 'Bracelets') && typeof selectedProduct === 'object' && (\n                <div style={{\n                  position: 'absolute',\n                  bottom: '-30px',\n                  left: '50%',\n                  transform: 'translateX(-50%)',\n                  fontSize: '11px',\n                  fontWeight: '600',\n                  color: 'white',\n                  backgroundColor: 'rgba(45, 140, 136, 0.9)',\n                  padding: '3px 8px',\n                  borderRadius: '12px',\n                  whiteSpace: 'nowrap',\n                  pointerEvents: 'none',\n                  boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n                  zIndex: 2\n                }}>\n                  {selectedProduct.braceletWidth || urlParams.size || '15'}mm width\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Camera-style Capture Button - Only show before capture */}\n        {!isCaptured && (\n          <div style={styles.captureButtonContainer}>\n            <div style={styles.captureButtonWrapper}>\n              <button\n                style={styles.captureBtn}\n                className={isMobile ? 'mobile-capture-btn' : ''}\n                onClick={handleCapture}\n                aria-label=\"Capture\"\n              >\n                <div style={styles.captureInner} className={isMobile ? 'mobile-inner-circle' : ''}></div>\n              </button>\n              <span style={styles.buttonLabel}>Capture</span>\n            </div>\n\n            {/* Try On Model Button */}\n            <div style={styles.modelButtonWrapper}>\n              <button\n                style={styles.modelBtn}\n                className={isMobile ? 'mobile-model-btn' : ''}\n                onClick={handleTryOnModel}\n                aria-label=\"Try on model\"\n              >\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"white\">\n                  <path d=\"M19,13H16.5L15.5,15H14L16,11H13V7H11V11H8L10,15H8.5L7.5,13H5C4.46,13 4,13.46 4,14V16C4,16.54 4.46,17 5,17H6V19C6,20.11 6.9,21 8,21H16C17.11,21 18,20.11 18,19V17H19C19.54,17 20,16.54 20,16V14C20,13.46 19.54,13 19,13M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2Z\"/>\n                </svg>\n              </button>\n              <span style={styles.buttonLabel}>Try Model</span>\n            </div>\n          </div>\n        )}\n\n        {/* Reset Button - Only visible when captured */}\n        {isCaptured && (\n          <button\n            style={styles.resetBtn}\n            onClick={() => window.location.reload()}\n            aria-label=\"Reset\"\n          >\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"white\">\n              <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z\"/>\n            </svg>\n          </button>\n        )}\n      </div>\n\n      {/* Mobile Wrist Size Button - Top right corner */}\n      {isCaptured && (\n        <button\n          style={styles.wristSizeFloatingBtn}\n          className={isMobile ? 'mobile-btn' : ''}\n          onClick={() => setShowWristSizeModal(true)}\n          aria-label=\"Adjust wrist size\"\n        >\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"white\">\n            <path d=\"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z\"/>\n          </svg>\n          <span style={styles.wristSizeText}>{userWristSize}mm</span>\n        </button>\n      )}\n\n      {/* Wrist Size Modal - Mobile-friendly popup */}\n      {showWristSizeModal && (\n        <div \n          style={styles.modalOverlay} \n          onClick={() => setShowWristSizeModal(false)}\n          className=\"modal-overlay\"\n        >\n          <div \n            style={styles.wristSizeModal} \n            onClick={(e) => e.stopPropagation()}\n            className=\"modal-content\"\n          >\n            <div style={styles.modalHeader}>\n              <h3 style={styles.modalTitle}>Adjust Wrist Size</h3>\n              <button\n                style={styles.modalCloseBtn}\n                onClick={() => setShowWristSizeModal(false)}\n                aria-label=\"Close\"\n              >\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\n                </svg>\n              </button>\n            </div>\n\n            <div style={styles.modalContent}>\n              {/* Gender Selection - Single Role */}\n              <div style={styles.genderSelection}>\n                <button\n                  style={{\n                    ...styles.genderButton,\n                    ...styles.genderButtonActive\n                  }}\n                  onClick={() => handleGenderChange('men')}\n                >\n                  Standard (50mm)\n                </button>\n              </div>\n\n              {/* Wrist Size Slider */}\n              <div style={styles.sliderContainer}>\n                <label style={styles.sliderLabel}>\n                  Wrist Size: {userWristSize}mm\n                </label>\n                <input\n                  type=\"range\"\n                  min={40}\n                  max={65}\n                  value={userWristSize}\n                  onChange={(e) => handleWristSizeChange(parseInt(e.target.value))}\n                  style={styles.slider}\n                />\n                <div style={styles.sliderLabels}>\n                  <span>40mm</span>\n                  <span>65mm</span>\n                </div>\n\n                {/* Quick Size Presets */}\n                <div style={styles.presetButtons}>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(DEFAULT_WRIST_SIZE)}\n                  >\n                    Average ({DEFAULT_WRIST_SIZE}mm)\n                  </button>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(40)}\n                  >\n                    Small (40mm)\n                  </button>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(65)}\n                  >\n                    Large (65mm)\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Product Selection Panel */}\n      {showProductSelection && (\n        <div\n          ref={panelRef}\n          style={{\n            ...styles.productSelection,\n            transform: `translateY(${panelPosition}px)`,\n            touchAction: 'none'\n          }}\n          className={isMobile ? 'mobile-product-panel' : ''}\n          aria-modal=\"true\"\n          role=\"dialog\"\n          onTouchStart={handleTouchStart}\n          onTouchMove={handleTouchMove}\n          onTouchEnd={handleTouchEnd}\n        >\n          <div \n            style={styles.dragHandle} \n            aria-hidden=\"true\"\n            onTouchStart={handleTouchStart}\n            onTouchMove={handleTouchMove}\n            onTouchEnd={handleTouchEnd}\n          />\n          {/* Only show tabs if no specific product type is provided via URL */}\n          {!urlParams.type && (\n            <div style={styles.productTabs}>\n              <button\n                style={{\n                  ...styles.tab,\n                  ...(activeTab === 'Watches' ? styles.activeTab : {})\n                }}\n                onClick={() => handleTabChange('Watches')}\n              >\n                Watches\n              </button>\n              <button\n                style={{\n                  ...styles.tab,\n                  ...(activeTab === 'Bracelets' ? styles.activeTab : {})\n                }}\n                onClick={() => handleTabChange('Bracelets')}\n              >\n                Bracelets\n              </button>\n            </div>\n          )}\n          <div style={styles.productScroll} className=\"product-scroll\">\n            {getCurrentProducts().length > 0 ? (\n              getCurrentProducts().map((product, index) => {\n                // Simple null check only\n                if (!product) return null;\n\n                const isSelected = (typeof selectedProduct === 'object' ? selectedProduct?.path : selectedProduct) === product.path;\n\n                return (\n                  <button\n                    key={index}\n                    style={{\n                      ...styles.productItem,\n                      borderColor: isSelected ? '#2D8C88' : '#e9ecef',\n                      backgroundColor: isSelected ? '#f0fffe' : '#ffffff'\n                    }}\n                    title={`${product.name} - ${product.caseDiameter || 'N/A'}mm`}\n                    onClick={() => handleProductSelect(product)}\n                    aria-label={`Select ${product.name} ${product.caseDiameter || 'N/A'}mm`}\n                  >\n                    <img\n                      src={product.path}\n                      alt={product.name}\n                      style={styles.productImage}\n                      onError={(e) => {\n                        e.target.parentElement.style.display = 'none';\n                      }}\n                    />\n                    <div style={styles.productLabel}>\n                      <div style={styles.productName}>{product.name}</div>\n                      {activeTab === 'Watches' && product.caseDiameter && (\n                        <div style={styles.productSize}>{product.caseDiameter}mm</div>\n                      )}\n                    </div>\n                  </button>\n                );\n              })\n            ) : (\n              <div style={styles.noProductsMessage}>\n                <div style={styles.noProductsIcon}>📱</div>\n                <div style={styles.noProductsTitle}>No Products Available</div>\n                <div style={styles.noProductsText}>\n                  This try-on experience is designed to be accessed through client websites with specific product parameters.\n                </div>\n                <div style={styles.noProductsSubtext}>\n                  Please visit a client's product page and click the \"Try On Virtually\" button.\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Styles object - Clean, modern design with improved mobile responsiveness\nconst styles = {\n  container: {\n    position: 'relative',\n    height: 'calc(var(--vh, 1vh) * 100)',\n    display: 'flex',\n    flexDirection: 'column',\n    backgroundColor: '#f8f9fa',\n    color: '#333',\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    overflow: 'hidden',\n    touchAction: 'manipulation',\n    WebkitTapHighlightColor: 'transparent',\n    WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS\n  },\n  cameraContainer: {\n    flex: 1,\n    position: 'relative',\n    overflow: 'hidden',\n    backgroundColor: '#000',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  cameraFeed: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    transform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  capturedImage: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    display: 'none',\n    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n\n  homeBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  backBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  switchContainer: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    zIndex: 10,\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '4px',\n    padding: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  switchTrack: {\n    position: 'absolute',\n    top: '12px',\n    left: '12px',\n    width: '60px',\n    height: '32px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    borderRadius: '16px',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    zIndex: 9,\n    transition: 'all 0.3s ease'\n  },\n  switchButton: {\n    position: 'relative',\n    width: '28px',\n    height: '28px',\n    borderRadius: '50%',\n    border: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 11,\n    margin: '2px',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    '&:hover': {\n      transform: 'scale(1.1)'\n    }\n  },\n  switchLabel: {\n    fontSize: '12px',\n    fontWeight: '700',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    marginTop: '4px',\n    padding: '4px 12px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    letterSpacing: '0.5px'\n  },\n  countdownDisplay: {\n    position: 'absolute',\n    top: '35%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '16px 24px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  countdownNumber: {\n    fontSize: '72px',\n    fontWeight: '900',\n    color: '#2D8C88',\n    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',\n    marginBottom: '8px',\n    animation: 'pulse 1s ease-in-out'\n  },\n  countdownText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'\n  },\n  statusMessage: {\n    position: 'absolute',\n    top: '25%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '12px 20px',\n    borderRadius: '16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  statusText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.9)',\n    padding: '12px 20px',\n    borderRadius: '25px',\n    marginBottom: '8px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtext: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '15px'\n  },\n  handGuide: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    width: '80%',\n    maxWidth: '400px',\n    height: 'auto',\n    opacity: 0.8,\n    pointerEvents: 'none',\n    zIndex: 5,\n    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    transition: 'all 0.3s ease'\n  },\nproductPosition: {\n  position: 'absolute',\n  top: '50%',\n  left: '50%',\n  transform: 'translate(-50%, -50%)',\n  zIndex: 8,\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  width: '25vw', // width controlled\n  aspectRatio: '1 / 1.6', // maintain height-to-width ratio (adjust as needed)\n  minWidth: '100px',\n  minHeight: '160px', // fallback for unsupported aspect-ratio\n  pointerEvents: 'none'\n}\n,\n  captureButtonContainer: {\n    position: 'absolute',\n    bottom: '30px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    display: 'flex',\n    alignItems: 'flex-end',\n    gap: '30px',\n    zIndex: 10\n  },\n  captureButtonWrapper: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '8px'\n  },\n  modelButtonWrapper: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '8px'\n  },\n  buttonLabel: {\n    fontSize: '12px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 3px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    padding: '4px 8px',\n    borderRadius: '12px',\n    whiteSpace: 'nowrap',\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)'\n  },\n  captureBtn: {\n    width: '80px',\n    height: '80px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '4px solid rgba(255, 255, 255, 0.3)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0,\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  captureInner: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: '#2D8C88',\n    borderRadius: '50%',\n    transition: 'all 0.2s ease'\n  },\n  modelBtn: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: 'rgba(45, 140, 136, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '3px solid rgba(255, 255, 255, 0.3)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0,\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  resetBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '50px',\n    height: '50px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n\n  // Wrist Size Floating Button - Follows CSS mobile patterns\n  wristSizeFloatingBtn: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    padding: '12px 16px',\n    borderRadius: '24px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    border: 'none',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    outline: 'none',\n    transition: 'all 0.2s ease',\n    zIndex: 15,\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)',\n    minHeight: '48px',\n    minWidth: '48px',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  wristSizeText: {\n    fontSize: '12px',\n    fontWeight: '600'\n  },\n\n  // Modal Styles\n  modalOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 50,\n    padding: '20px',\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)',\n    touchAction: 'none'\n  },\n  wristSizeModal: {\n    backgroundColor: 'white',\n    borderRadius: '24px',\n    width: '100%',\n    maxWidth: '95vw',\n    maxHeight: '80vh',\n    overflow: 'hidden',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',\n    display: 'flex',\n    flexDirection: 'column',\n    margin: '10px',\n    position: 'relative'\n  },\n  modalHeader: {\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    padding: '16px 20px 12px 20px',\n    borderBottom: '1px solid #f0f0f0'\n  },\n  modalTitle: {\n    fontSize: '18px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0\n  },\n  modalCloseBtn: {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    border: 'none',\n    backgroundColor: '#f5f5f5',\n    color: '#666',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'all 0.2s ease',\n    outline: 'none'\n  },\n  modalContent: {\n    padding: '16px 20px 20px 20px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '16px',\n    overflowY: 'auto',\n    WebkitOverflowScrolling: 'touch',\n    maxHeight: 'calc(80vh - 60px)'\n  },\n\n  // Mobile styles handled by CSS file\n  wristSizeContent: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '12px'\n  },\n  wristSizeTitle: {\n    fontSize: '20px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0,\n    textAlign: 'center'\n  },\n  wristSizeSubtitle: {\n    fontSize: '14px',\n    color: '#666',\n    margin: 0,\n    textAlign: 'center',\n    lineHeight: '1.4'\n  },\n  genderSelection: {\n    display: 'flex',\n    gap: '12px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  genderButton: {\n    flex: 1,\n    padding: '12px 16px',\n    borderRadius: '10px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '2px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  genderButtonActive: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderColor: '#2D8C88',\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'\n  },\n  sliderContainer: {\n    width: '100%',\n    maxWidth: '300px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '8px'\n  },\n  sliderLabel: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: '#333',\n    textAlign: 'center',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px'\n  },\n  sizeChange: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    backgroundColor: 'rgba(45, 140, 136, 0.1)',\n    padding: '2px 8px',\n    borderRadius: '12px'\n  },\n  slider: {\n    width: '100%',\n    height: '6px',\n    borderRadius: '3px',\n    background: '#e0e0e0',\n    outline: 'none',\n    cursor: 'pointer',\n    WebkitAppearance: 'none',\n    appearance: 'none'\n  },\n  sliderLabels: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    fontSize: '12px',\n    color: '#999',\n    marginTop: '4px'\n  },\n  presetButtons: {\n    display: 'flex',\n    gap: '8px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  presetButton: {\n    flex: 1,\n    padding: '8px 12px',\n    borderRadius: '8px',\n    fontSize: '12px',\n    fontWeight: '500',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  continueButton: {\n    width: '100%',\n    maxWidth: '300px',\n    padding: '14px 24px',\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderRadius: '12px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n\n  // Fancy Popup Styles\n  fancyPopupModal: {\n    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',\n    borderRadius: '24px',\n    width: '100%',\n    maxWidth: '380px',\n    overflow: 'hidden',\n    boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 32px rgba(45, 140, 136, 0.1)',\n    display: 'flex',\n    flexDirection: 'column',\n    margin: '10px',\n    position: 'relative',\n    border: '1px solid rgba(45, 140, 136, 0.1)'\n  },\n  fancyPopupHeader: {\n    textAlign: 'center',\n    padding: '32px 24px 24px 24px',\n    background: 'linear-gradient(135deg, #2D8C88 0%, #1a6b67 100%)',\n    color: 'white',\n    position: 'relative'\n  },\n  fancyPopupTitle: {\n    fontSize: '24px',\n    fontWeight: '700',\n    marginBottom: '8px',\n    textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'\n  },\n  fancyPopupSubtitle: {\n    fontSize: '14px',\n    fontWeight: '400',\n    opacity: 0.9,\n    letterSpacing: '0.5px'\n  },\n  fancyPopupContent: {\n    padding: '24px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '20px'\n  },\n  fancyPopupPoint: {\n    display: 'flex',\n    alignItems: 'flex-start',\n    gap: '16px'\n  },\n  fancyPointNumber: {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    background: 'linear-gradient(135deg, #2D8C88 0%, #1a6b67 100%)',\n    color: 'white',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontSize: '14px',\n    fontWeight: '700',\n    flexShrink: 0,\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n  fancyPointText: {\n    fontSize: '15px',\n    lineHeight: '1.5',\n    color: '#333',\n    flex: 1,\n    paddingTop: '4px'\n  },\n  fancyOkButton: {\n    position: 'relative',\n    width: '100%',\n    padding: '16px 24px',\n    background: 'linear-gradient(135deg, #2D8C88 0%, #1a6b67 100%)',\n    color: '#ffffff',\n    borderRadius: '0 0 24px 24px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    border: 'none',\n    outline: 'none',\n    overflow: 'hidden'\n  },\n  fancyButtonText: {\n    position: 'relative',\n    zIndex: 2\n  },\n  fancyButtonGlow: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%)',\n    opacity: 0,\n    transition: 'opacity 0.3s ease'\n  },\n\n  // Clean Instruction Styles\n  instructionContainer: {\n    position: 'absolute',\n    top: '28%', // moved further down from 20%\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 19,\n    pointerEvents: 'none'\n  },\n  instructionText: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: 'white',\n    textAlign: 'center',\n    padding: '8px 16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '12px',\n    backdropFilter: 'blur(8px)',\n    WebkitBackdropFilter: 'blur(8px)',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)',\n    border: '1px solid rgba(255, 255, 255, 0.1)',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)'\n  },\n\n\n\n  productSelection: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.98)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '24px',\n    borderTopRightRadius: '24px',\n    padding: '16px',\n    maxHeight: '85vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 20,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none',\n    transform: 'translateY(0)',\n    transition: 'transform 0.3s ease-out',\n    overflow: 'hidden',\n    touchAction: 'none',\n    willChange: 'transform',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n  closeBtn: {\n    position: 'absolute',\n    top: '12px',\n    right: '16px',\n    color: '#666',\n    cursor: 'pointer',\n    zIndex: 21,\n    width: '32px',\n    height: '32px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderRadius: '50%',\n    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    padding: 0\n  },\n  productTabs: {\n    display: 'flex',\n    marginBottom: '16px',\n    backgroundColor: '#f0f0f0',\n    borderRadius: '12px',\n    padding: '4px',\n    gap: '4px'\n  },\n  tab: {\n    flex: 1,\n    textAlign: 'center',\n    padding: '12px 16px',\n    borderRadius: '8px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    color: '#666',\n    outline: 'none',\n    border: 'none',\n    backgroundColor: 'transparent',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  activeTab: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'\n  },\n  productScroll: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(100px, 1fr))',\n    gap: '12px',\n    maxHeight: 'calc(85vh - 120px)',\n    overflowY: 'auto',\n    paddingBottom: '16px',\n    scrollbarWidth: 'thin',\n    scrollbarColor: '#ddd #f5f5f5',\n    WebkitOverflowScrolling: 'touch'\n  },\n  productItem: {\n    position: 'relative',\n    width: '100%',\n    aspectRatio: '1/1',\n    backgroundColor: '#ffffff',\n    borderRadius: '16px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    overflow: 'hidden',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',\n    padding: '8px',\n    outline: 'none',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  productImage: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'contain',\n    borderRadius: '8px',\n    backgroundColor: '#ffffff'\n  },\n  productLabel: {\n    position: 'absolute',\n    bottom: '3px',\n    left: '3px',\n    right: '3px',\n    fontSize: '9px',\n    color: '#666',\n    textAlign: 'center',\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    borderRadius: '3px',\n    padding: '3px 2px',\n    overflow: 'hidden'\n  },\n  productName: {\n    fontSize: '9px',\n    fontWeight: '600',\n    whiteSpace: 'nowrap',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    marginBottom: '1px'\n  },\n  productSize: {\n    fontSize: '8px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    whiteSpace: 'nowrap'\n  },\n  dragHandle: {\n    width: '40px',\n    height: '4px',\n    backgroundColor: '#e0e0e0',\n    borderRadius: '2px',\n    margin: '0 auto 12px',\n    cursor: 'grab',\n    touchAction: 'none',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n\n  desktopContainer: {\n    position: 'relative',\n    height: '100vh',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    backgroundColor: '#f8f9fa',\n    padding: '20px'\n  },\n  qrContainer: {\n    backgroundColor: 'white',\n    padding: '40px',\n    borderRadius: '24px',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',\n    textAlign: 'center',\n    maxWidth: '500px',\n    width: '100%'\n  },\n  qrTitle: {\n    fontSize: '28px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    marginBottom: '16px'\n  },\n  qrSubtitle: {\n    fontSize: '16px',\n    color: '#666',\n    marginBottom: '32px',\n    lineHeight: '1.5'\n  },\n  qrWrapper: {\n    backgroundColor: 'white',\n    padding: '20px',\n    borderRadius: '16px',\n    display: 'inline-block',\n    marginBottom: '24px',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'\n  },\n  qrLink: {\n    fontSize: '14px',\n    color: '#2D8C88',\n    marginBottom: '32px',\n    wordBreak: 'break-all'\n  },\n  clientInfo: {\n    fontSize: '12px',\n    color: '#666',\n    marginBottom: '16px',\n    fontStyle: 'italic'\n  },\n  noProductsMessage: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n    padding: '40px 20px',\n    textAlign: 'center',\n    height: '200px'\n  },\n  noProductsIcon: {\n    fontSize: '48px',\n    marginBottom: '16px'\n  },\n  noProductsTitle: {\n    fontSize: '18px',\n    fontWeight: '600',\n    color: '#333',\n    marginBottom: '12px'\n  },\n  noProductsText: {\n    fontSize: '14px',\n    color: '#666',\n    lineHeight: '1.5',\n    marginBottom: '8px',\n    maxWidth: '280px'\n  },\n  noProductsSubtext: {\n    fontSize: '12px',\n    color: '#999',\n    lineHeight: '1.4',\n    maxWidth: '260px'\n  },\n\n  // Disclaimer Popup Styles\n  disclaimerOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.7)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 1000,\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)'\n  },\n  disclaimerPopup: {\n    backgroundColor: 'white',\n    borderRadius: '20px',\n    padding: '0',\n    maxWidth: '400px',\n    width: '90%',\n    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.2)'\n  },\n  disclaimerContent: {\n    padding: '32px 24px',\n    textAlign: 'center'\n  },\n  disclaimerTitle: {\n    fontSize: '24px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    marginBottom: '24px',\n    margin: '0 0 24px 0'\n  },\n  disclaimerPoints: {\n    marginBottom: '32px'\n  },\n  disclaimerPoint: {\n    fontSize: '16px',\n    color: '#333',\n    lineHeight: '1.6',\n    marginBottom: '16px',\n    margin: '0 0 16px 0',\n    textAlign: 'center'\n  },\n  disclaimerButton: {\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    border: 'none',\n    borderRadius: '12px',\n    padding: '14px 32px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)',\n    ':hover': {\n      backgroundColor: '#258A86',\n      transform: 'translateY(-1px)',\n      boxShadow: '0 6px 16px rgba(45, 140, 136, 0.4)'\n    }\n  },\n\n  // Clean Auto Capture Toggle\n  autoCaptureToggle: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    zIndex: 20,\n    padding: '8px 12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '16px',\n    backdropFilter: 'blur(8px)',\n    WebkitBackdropFilter: 'blur(8px)',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  autoCaptureLabel: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)'\n  },\n\n  // Clean Branding Styles\n  brandingContainer: {\n    position: 'absolute',\n    top: '80px',\n    left: '20px',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '6px',\n    zIndex: 15,\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '16px',\n    backdropFilter: 'blur(8px)',\n    WebkitBackdropFilter: 'blur(8px)',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  brandingLogo: {\n    width: '22px',\n    height: '22px',\n    objectFit: 'contain',\n    marginRight: '7px',\n    display: 'inline-block',\n    verticalAlign: 'middle'\n  },\n  brandingText: {\n    fontSize: '11px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)'\n  },\n\n  // Add new styles for smaller status message\n  statusMessageSmall: {\n    position: 'absolute',\n    top: '13%', // move up so it's above the SVG shape\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '6px 12px',\n    borderRadius: '10px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(8px)',\n    WebkitBackdropFilter: 'blur(8px)',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'\n  },\n  statusTextSmall: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.8)',\n    padding: '6px 12px',\n    borderRadius: '15px',\n    marginBottom: '4px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtextSmall: {\n    fontSize: '10px',\n    fontWeight: '400',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    padding: '4px 8px',\n    borderRadius: '10px'\n  },\n\n  // Clean Branding - Tangiblee style\n  brandingContainerTangiblee: {\n    position: 'absolute',\n    top: '32px',\n    left: '12px',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '14px',\n    zIndex: 15,\n    backgroundColor: 'rgba(0,0,0,0.0)', // fully transparent\n    padding: '0',\n    borderRadius: '0',\n    boxShadow: 'none',\n  },\n  brandingLogoTangiblee: {\n    width: '38px',\n    height: '38px',\n    objectFit: 'contain',\n    display: 'inline-block',\n    verticalAlign: 'middle',\n  },\n  brandingTextStacked: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'flex-start',\n    lineHeight: 1.1,\n  },\n  poweredByText: {\n    fontSize: '15px',\n    color: 'white',\n    fontWeight: 400,\n    opacity: 0.85,\n    letterSpacing: '0.01em',\n    marginBottom: '1px',\n  },\n  viatryonText: {\n    fontSize: '28px',\n    color: 'white',\n    fontWeight: 600,\n    letterSpacing: '0.01em',\n    marginTop: '0',\n  },\n};\n\nexport default Tryon;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,SAAS,QAAQ,cAAc,CAAC,CAAC;AAC1C,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,uBAAuB,QAAQ,4BAA4B;;AAEpE;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,SAAS,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,CAACC,cAAc,CAAC,0BAA0B,CAAC,EAAE;EAC3F,MAAMC,YAAY,GAAGF,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EACpDD,YAAY,CAACE,EAAE,GAAG,0BAA0B;EAC5CF,YAAY,CAACG,WAAW,GAAGN,SAAS;EACpCC,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACL,YAAY,CAAC;AACzC;AAEA,MAAMM,KAAK,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAClC;EACA,MAAMC,QAAQ,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMqB,gBAAgB,GAAGrB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMsB,SAAS,GAAGtB,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACqC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAAC2C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC;IACzC+C,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyD,eAAe,EAAEC,kBAAkB,CAAC,GAAG1D,QAAQ,CAAC;IACrD2D,sBAAsB,EAAE,IAAI;IAC5BC,eAAe,EAAE,CAAC;IAClBC,wBAAwB,EAAE,KAAK;IAC/BC,oBAAoB,EAAE,KAAK;IAC3BC,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE,KAAK;IACxBC,eAAe,EAAE,CAAC;IAClBC,WAAW,EAAE,CAAC;IACdC,cAAc,EAAE,CAAC;IACjBC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE;MACZC,YAAY,EAAE,CAAC;MACfC,eAAe,EAAE,CAAC;MAClBC,cAAc,EAAE,CAAC;MACjBC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC;MACbC,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;IACR,CAAC;IACDC,UAAU,EAAE;MACVC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,IAAI;MACfC,gBAAgB,EAAE;IACpB,CAAC;IACDC,gBAAgB,EAAE;MAChBC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE;IACZ;EACF,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxF,QAAQ,CAAC;IAC3DyF,SAAS,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC;IACnDC,WAAW,EAAE;MAAEC,IAAI,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC;IAClCC,cAAc,EAAE,CAAC,CAAC;IAClBC,aAAa,EAAE,CAAC,CAAC;IACjBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrG,QAAQ,CAAC;IACnDsG,qBAAqB,EAAE,CAAC;IACxBC,wBAAwB,EAAE,CAAC;IAC3BC,kBAAkB,EAAE,CAAC;IACrBC,qBAAqB,EAAE,CAAC;IACxBC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5G,QAAQ,CAAC;IACvD6G,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE,KAAK;IACrBC,cAAc,EAAE,KAAK;IACrBC,YAAY,EAAE,KAAK;IACnBC,WAAW,EAAE,KAAK;IAClBC,mBAAmB,EAAE,KAAK;IAC1BC,iBAAiB,EAAE;EACrB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrH,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAACsH,aAAa,EAAEC,gBAAgB,CAAC,GAAGvH,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACwH,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzH,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAErE;EACA,MAAM,CAAC0H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3H,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC4H,SAAS,EAAEC,YAAY,CAAC,GAAG7H,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC8H,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/H,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjI,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACkI,aAAa,EAAEC,gBAAgB,CAAC,GAAGnI,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACoI,UAAU,EAAEC,aAAa,CAAC,GAAGrI,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsI,MAAM,EAAEC,SAAS,CAAC,GAAGvI,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAMwI,QAAQ,GAAGtI,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACA,MAAMuI,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,0BAA0B;IAChC;IACAC,YAAY,EAAE,EAAE;IAAE;IAClBC,aAAa,EAAE,IAAI;IAAE;IACrBC,UAAU,EAAE,EAAE;IAAE;IAChBC,WAAW,EAAE,EAAE;IAAE;IACjBC,YAAY,EAAE,EAAE;IAAE;IAClB9F,IAAI,EAAE,OAAO;IACb+F,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,qBAAqB;IAC3B;IACAC,YAAY,EAAE,EAAE;IAAE;IAClBC,aAAa,EAAE,IAAI;IAAE;IACrBC,UAAU,EAAE,EAAE;IAAE;IAChBC,WAAW,EAAE,IAAI;IAAE;IACnBC,YAAY,EAAE,EAAE;IAAE;IAClB9F,IAAI,EAAE,OAAO;IACb+F,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,qBAAqB;IAC3B;IACAC,YAAY,EAAE,EAAE;IAAE;IAClBC,aAAa,EAAE,GAAG;IAAE;IACpBC,UAAU,EAAE,EAAE;IAAE;IAChBC,WAAW,EAAE,EAAE;IAAE;IACjBC,YAAY,EAAE,EAAE;IAAE;IAClB9F,IAAI,EAAE,QAAQ;IACd+F,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,qBAAqB;IAC3B;IACAC,YAAY,EAAE,EAAE;IAAE;IAClBC,aAAa,EAAE,IAAI;IAAE;IACrBC,UAAU,EAAE,EAAE;IAAE;IAChBC,WAAW,EAAE,EAAE;IAAE;IACjBC,YAAY,EAAE,EAAE;IAAE;IAClB9F,IAAI,EAAE,YAAY;IAClB+F,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,qBAAqB;IAC3B;IACAC,YAAY,EAAE,EAAE;IAAE;IAClBC,aAAa,EAAE,CAAC;IAAE;IAClBC,UAAU,EAAE,EAAE;IAAE;IAChBC,WAAW,EAAE,EAAE;IAAE;IACjBC,YAAY,EAAE,EAAE;IAAE;IAClB9F,IAAI,EAAE,YAAY;IAClB+F,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,qBAAqB;IAC3B;IACAC,YAAY,EAAE,EAAE;IAAE;IAClBC,aAAa,EAAE,EAAE;IAAE;IACnBC,UAAU,EAAE,EAAE;IAAE;IAChBC,WAAW,EAAE,EAAE;IAAE;IACjBC,YAAY,EAAE,EAAE;IAAE;IAClB9F,IAAI,EAAE,SAAS;IACf+F,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,SAAS,GAAG,CAChB;IAAER,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC1D;IAAED,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE;EAA2B,CAAC,EACzD;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC1D;IAAED,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC5D;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC1D;IAAED,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAA2B,CAAC,CAC7D;;EAED;EACA,MAAMQ,gBAAgB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,WAAW,GAAG,OAAO,KAAK;IACpE,IAAI;MACF,IAAIC,iBAAiB,GAAGF,UAAU,CAACG,GAAG;;MAEtC;MACA,IAAIF,WAAW,KAAK,UAAU,EAAE;QAC9B,IAAI;UACF;UACAC,iBAAiB,GAAG,MAAMlJ,YAAY,CAACgJ,UAAU,CAACG,GAAG,CAAC;UACtDC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;;UAEhE;UACAH,iBAAiB,GAAG,MAAMjJ,uBAAuB,CAACiJ,iBAAiB,EAAED,WAAW,CAAC;UACjFG,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;;UAE/D;UACAL,UAAU,CAACG,GAAG,GAAGD,iBAAiB;QAEpC,CAAC,CAAC,OAAOI,KAAK,EAAE;UACdF,OAAO,CAACG,IAAI,CAAC,sCAAsC,EAAED,KAAK,CAAC;UAC3D;UACA,MAAME,sBAAsB,CAACR,UAAU,EAAEC,WAAW,CAAC;QACvD;MACF,CAAC,MAAM;QACL;QACA,MAAMQ,iBAAiB,GAAG,MAAMxJ,uBAAuB,CAACiJ,iBAAiB,EAAED,WAAW,CAAC;QACvFD,UAAU,CAACG,GAAG,GAAGM,iBAAiB;MACpC;;MAEA;MACAT,UAAU,CAACU,KAAK,CAACC,MAAM,GAAG,MAAM;MAChCX,UAAU,CAACU,KAAK,CAACE,YAAY,GAAG,QAAQ;MACxCZ,UAAU,CAACU,KAAK,CAACG,OAAO,GAAG,GAAG;MAE9BT,OAAO,CAACC,GAAG,CAAC,oCAAoCJ,WAAW,EAAE,CAAC;IAEhE,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdF,OAAO,CAACG,IAAI,CAAC,qCAAqC,EAAED,KAAK,CAAC;MAC1D;MACA,MAAME,sBAAsB,CAACR,UAAU,EAAEC,WAAW,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMO,sBAAsB,GAAG,MAAAA,CAAOR,UAAU,EAAEC,WAAW,KAAK;IAChE,IAAI;MACFG,OAAO,CAACC,GAAG,CAAC,4CAA4CJ,WAAW,EAAE,CAAC;MAEtE,IAAIA,WAAW,KAAK,UAAU,EAAE;QAC9B;QACA,MAAMa,YAAY,GAAG,MAAM9J,YAAY,CAACgJ,UAAU,CAACG,GAAG,CAAC;QACvDH,UAAU,CAACG,GAAG,GAAGW,YAAY;MAC/B;;MAEA;MACA,MAAMC,MAAM,GAAGxJ,QAAQ,CAACG,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMsJ,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnCF,MAAM,CAACG,KAAK,GAAGlB,UAAU,CAACmB,YAAY;MACtCJ,MAAM,CAACK,MAAM,GAAGpB,UAAU,CAACqB,aAAa;MACxCL,GAAG,CAACM,SAAS,CAACtB,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;MAE/B,MAAMuB,SAAS,GAAGP,GAAG,CAACQ,YAAY,CAAC,CAAC,EAAE,CAAC,EAAET,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;MACrE,MAAMK,IAAI,GAAGF,SAAS,CAACE,IAAI;;MAE3B;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACE,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QACvC,MAAME,CAAC,GAAGH,IAAI,CAACC,CAAC,CAAC;QACjB,MAAMG,CAAC,GAAGJ,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC;QACrB,MAAMI,CAAC,GAAGL,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC;;QAErB;QACA,MAAMK,SAAS,GAAG9B,WAAW,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;QACrD,IAAI2B,CAAC,GAAGG,SAAS,IAAIF,CAAC,GAAGE,SAAS,IAAID,CAAC,GAAGC,SAAS,IAC/CC,IAAI,CAACC,GAAG,CAACL,CAAC,GAAGC,CAAC,CAAC,GAAG,CAAC,IAAIG,IAAI,CAACC,GAAG,CAACJ,CAAC,GAAGC,CAAC,CAAC,GAAG,CAAC,EAAE;UAC9CL,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACnB;MACF;MAEAV,GAAG,CAACkB,YAAY,CAACX,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;MACjCvB,UAAU,CAACG,GAAG,GAAGY,MAAM,CAACoB,SAAS,CAAC,WAAW,CAAC;MAE9C/B,OAAO,CAACC,GAAG,CAAC,6CAA6CJ,WAAW,EAAE,CAAC;IACzE,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D;EACF,CAAC;;EAED;EACA,MAAM8B,qBAAqB,GAAIb,SAAS,IAAK;IAC3C;IACA,OAAOS,IAAI,CAACK,MAAM,CAAC,CAAC,GAAG,GAAG;EAC5B,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UACLC,UAAU,EAAE,aAAa;UACzB1B,KAAK,EAAE;YAAE2B,KAAK,EAAE;UAAK,CAAC;UACtBzB,MAAM,EAAE;YAAEyB,KAAK,EAAE;UAAK;QACxB;MACF,CAAC,CAAC;MACF,IAAI3K,QAAQ,CAAC4K,OAAO,EAAE;QACpB5K,QAAQ,CAAC4K,OAAO,CAACC,SAAS,GAAGR,MAAM;MACrC;IACF,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZ5C,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAE0C,GAAG,CAAC;MAC7C;MACA,IAAI7K,gBAAgB,CAAC2K,OAAO,EAAE;QAC5B3K,gBAAgB,CAAC2K,OAAO,CAAC3C,GAAG,GAAG,iBAAiB;QAChDhI,gBAAgB,CAAC2K,OAAO,CAACpC,KAAK,CAACuC,OAAO,GAAG,OAAO;MAClD;MACA7C,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/C/H,aAAa,CAAC,IAAI,CAAC;MACnBM,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMsK,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAChL,QAAQ,CAAC4K,OAAO,IAAI,CAAC1K,SAAS,CAAC0K,OAAO,EAAE,OAAO,IAAI;IAExD,MAAM/B,MAAM,GAAG3I,SAAS,CAAC0K,OAAO;IAChC,MAAMH,KAAK,GAAGzK,QAAQ,CAAC4K,OAAO;IAC9B/B,MAAM,CAACG,KAAK,GAAGyB,KAAK,CAACQ,UAAU;IAC/BpC,MAAM,CAACK,MAAM,GAAGuB,KAAK,CAACS,WAAW;IACjC,MAAMpC,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACnCD,GAAG,CAACM,SAAS,CAACqB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE5B,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;IACvD,OAAOL,MAAM,CAACoB,SAAS,CAAC,WAAW,CAAC;EACtC,CAAC;;EAID;EACAtL,SAAS,CAAC,MAAM;IACd,MAAMwM,cAAc,GAAGA,CAAA,KAAM;MAC3B,MAAMC,eAAe,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;MACnE,MAAMC,MAAM,GAAG;QACbhK,KAAK,EAAE2J,eAAe,CAACM,GAAG,CAAC,OAAO,CAAC;QACnChK,MAAM,EAAE0J,eAAe,CAACM,GAAG,CAAC,QAAQ,CAAC;QACrC/J,IAAI,EAAEyJ,eAAe,CAACM,GAAG,CAAC,MAAM,CAAC;QACjC9J,IAAI,EAAEwJ,eAAe,CAACM,GAAG,CAAC,MAAM;MAClC,CAAC;MAEDxD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEsD,MAAM,CAAC;MAC7CjK,YAAY,CAACiK,MAAM,CAAC;;MAEpB;MACA,IAAIA,MAAM,CAAChK,KAAK,EAAE;QAChB,IAAI;UACF;UACA,MAAMkK,QAAQ,GAAG,IAAIC,GAAG,CAACH,MAAM,CAAChK,KAAK,CAAC;UAEtC,MAAMsG,WAAW,GAAG0D,MAAM,CAAC7J,IAAI,IAAI,SAAS;UAC5C,MAAMiK,WAAW,GAAG9D,WAAW,KAAK,WAAW,GAAG,EAAE,GAAG,EAAE;UACzD,MAAM+D,UAAU,GAAGC,QAAQ,CAACN,MAAM,CAAC9J,IAAI,CAAC,IAAIkK,WAAW;UAEvDvL,kBAAkB,CAAC;YACjB8G,IAAI,EAAE,gBAAgB;YACtBC,IAAI,EAAEoE,MAAM,CAAChK,KAAK;YAClB6F,YAAY,EAAES,WAAW,KAAK,SAAS,GAAG+D,UAAU,GAAG,IAAI;YAC3DE,aAAa,EAAEjE,WAAW,KAAK,WAAW,GAAG+D,UAAU,GAAG,IAAI;YAC9DvE,aAAa,EAAEQ,WAAW,KAAK,SAAS,GAAG,EAAE,GAAG,IAAI;YACpDP,UAAU,EAAEsE,UAAU;YACtBrE,WAAW,EAAEM,WAAW,KAAK,SAAS,GAAG+D,UAAU,GAAG,IAAI,GAAGA,UAAU,GAAG,CAAC;YAC3EpE,YAAY,EAAEK,WAAW,KAAK,SAAS,GAAG+D,UAAU,GAAG,IAAI,GAAG,IAAI;YAClElK,IAAI,EAAEmG,WAAW;YACjBkE,QAAQ,EAAER,MAAM,CAAC/J;UACnB,CAAC,CAAC;UACFhB,uBAAuB,CAAC,KAAK,CAAC;UAE9BwH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;YACtC1G,KAAK,EAAEgK,MAAM,CAAChK,KAAK;YACnBC,MAAM,EAAE+J,MAAM,CAAC/J,MAAM;YACrBC,IAAI,EAAE8J,MAAM,CAAC9J;UACf,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOyG,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEqD,MAAM,CAAChK,KAAK,CAAC;UAC1D;UACAf,uBAAuB,CAAC,IAAI,CAAC;QAC/B;MACF;IACF,CAAC;IAEDyK,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxM,SAAS,CAAC,MAAM;IACd,IAAI4C,SAAS,CAACG,MAAM,IAAIH,SAAS,CAACE,KAAK,EAAE;MACvCyG,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE5G,SAAS,CAAC;MACjE2K,qBAAqB,CAAC,CAAC;;MAEvB;MACA,MAAMC,OAAO,GAAGC,0BAA0B,CAAC,CAAC;;MAE5C;MACA9G,mBAAmB,CAAC+G,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP7G,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;;MAEH;MACA,MAAM8G,kBAAkB,GAAGA,CAAA,KAAM;QAC/BC,mBAAmB,CAAC,WAAW,CAAC;MAClC,CAAC;MAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;QACnC,IAAInN,QAAQ,CAACoN,eAAe,KAAK,QAAQ,EAAE;UACzCF,mBAAmB,CAAC,WAAW,CAAC;QAClC;MACF,CAAC;MAEDjB,MAAM,CAACoB,gBAAgB,CAAC,cAAc,EAAEJ,kBAAkB,CAAC;MAC3DjN,QAAQ,CAACqN,gBAAgB,CAAC,kBAAkB,EAAEF,sBAAsB,CAAC;MAErE,OAAO,MAAM;QACXL,OAAO,CAAC,CAAC;QACTb,MAAM,CAACqB,mBAAmB,CAAC,cAAc,EAAEL,kBAAkB,CAAC;QAC9DjN,QAAQ,CAACsN,mBAAmB,CAAC,kBAAkB,EAAEH,sBAAsB,CAAC;QACxED,mBAAmB,CAAC,WAAW,CAAC;MAClC,CAAC;IACH,CAAC,MAAM;MACLrE,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE;QACxDyE,SAAS,EAAE,CAAC,CAACrL,SAAS,CAACG,MAAM;QAC7BmL,QAAQ,EAAE,CAAC,CAACtL,SAAS,CAACE;MACxB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;;EAEf;EACA5C,SAAS,CAAC,MAAM;IACd,MAAMmO,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMC,cAAc,GAAGzB,MAAM,CAAC0B,UAAU,IAAI,GAAG;MAC/ChM,WAAW,CAAC+L,cAAc,CAAC;MAC3B7L,YAAY,CAAC,CAAC6L,cAAc,CAAC;IAC/B,CAAC;;IAED;IACA,MAAME,KAAK,GAAGA,CAAA,KAAM;MAClB,MAAMC,EAAE,GAAG5B,MAAM,CAAC6B,WAAW,GAAG,IAAI;MACpC9N,QAAQ,CAAC+N,eAAe,CAAC5E,KAAK,CAAC6E,WAAW,CAAC,MAAM,EAAE,GAAGH,EAAE,IAAI,CAAC;IAC/D,CAAC;IAEDJ,WAAW,CAAC,CAAC;IACbG,KAAK,CAAC,CAAC;IAEP3B,MAAM,CAACoB,gBAAgB,CAAC,QAAQ,EAAE,MAAM;MACtCI,WAAW,CAAC,CAAC;MACbG,KAAK,CAAC,CAAC;IACT,CAAC,CAAC;IAEF3B,MAAM,CAACoB,gBAAgB,CAAC,mBAAmB,EAAE,MAAM;MACjDY,UAAU,CAAC,MAAM;QACfL,KAAK,CAAC,CAAC;MACT,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEF,OAAO,MAAM;MACX3B,MAAM,CAACqB,mBAAmB,CAAC,QAAQ,EAAEG,WAAW,CAAC;MACjDxB,MAAM,CAACqB,mBAAmB,CAAC,mBAAmB,EAAEM,KAAK,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtO,SAAS,CAAC,MAAM;IACd,IAAI,OAAO2M,MAAM,KAAK,WAAW,IAAIhB,SAAS,CAACC,YAAY,EAAE;MAC3DH,UAAU,CAAC,CAAC;IACd;IACA;IACAhJ,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMmM,kBAAkB,GAAG,EAAE,CAAC,CAAC;EAC/B,MAAMC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,MAAMC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,MAAMC,iBAAiB,GAAG,EAAE,CAAC,CAAC;;EAE9B;EACA,MAAMC,mBAAmB,GAAG;IAC1BC,GAAG,EAAE,EAAE,CAAI;EACb,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,EAAE,CAAC,CAAC;EAC9B,MAAMC,uBAAuB,GAAG,EAAE,CAAC,CAAC;;EAEpC,MAAMC,yBAAyB,GAAG,GAAG,CAAC,CAAC;EACvC,MAAMC,iBAAiB,GAAG,GAAG,CAAC,CAAC;EAC/B,MAAMC,kBAAkB,GAAG,GAAG,CAAC,CAAC;;EAEhC;EACA,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;EACxB,MAAMC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,MAAMC,YAAY,GAAG,EAAE,CAAC,CAAC;EACzB,MAAMC,eAAe,GAAG,EAAE,CAAC,CAAC;;EAE5B;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAEC,cAAc,EAAEC,eAAe,KAAK;IAC3E;IACA,MAAMC,gBAAgB,GAAGf,mBAAmB,CAAC7H,UAAU,CAAC;;IAExD;IACA,MAAM6I,iBAAiB,GAAG7E,IAAI,CAACxF,GAAG,CAAC0B,aAAa,GAAG6H,iBAAiB,EAAEC,uBAAuB,CAAC;;IAE9F;IACA,MAAMc,qBAAqB,GAAGF,gBAAgB,GAAGC,iBAAiB;;IAElE;IACA,MAAME,YAAY,GAAGd,yBAAyB,GAAGY,iBAAiB;;IAElE;IACA,MAAMG,aAAa,GAAGP,KAAK,CAAC/G,UAAU,GAAGqH,YAAY;IACrD,MAAME,cAAc,GAAGR,KAAK,CAAC9G,WAAW,GAAGoH,YAAY;IACvD,MAAMG,eAAe,GAAGT,KAAK,CAAC7G,YAAY,GAAGmH,YAAY;;IAEzD;IACA,MAAMI,iBAAiB,GAAIH,aAAa,GAAGd,iBAAiB,GAAI,GAAG;IACnE,MAAMkB,kBAAkB,GAAIH,cAAc,GAAGd,kBAAkB,GAAI,GAAG;IACtE,MAAMkB,mBAAmB,GAAIH,eAAe,GAAGhB,iBAAiB,GAAI,GAAG;;IAEvE;IACA,MAAMoB,SAAS,GAAG,EAAE,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,EAAE,CAAC,CAAC;;IAEtB,OAAO;MACLrG,KAAK,EAAEc,IAAI,CAACxF,GAAG,CAAC2K,iBAAiB,EAAE,CAAC,CAAC;MAAE;MACvC/F,MAAM,EAAEY,IAAI,CAACxF,GAAG,CAAC4K,kBAAkB,EAAE,EAAE,CAAC;MAAE;MAC1CxH,YAAY,EAAEyH,mBAAmB;MACjCC,SAAS;MACTC,SAAS;MACTC,KAAK,EAAExF,IAAI,CAACzF,GAAG,CAAC4K,iBAAiB,GAAG,EAAE,EAAEC,kBAAkB,GAAG,EAAE,CAAC,GAAGN,qBAAqB;MACxFW,SAAS,EAAEhB,KAAK,CAAC/G,UAAU;MAC3BgI,UAAU,EAAEjB,KAAK,CAAC9G,WAAW;MAC7BH,YAAY,EAAEiH,KAAK,CAACjH,YAAY;MAChCmI,cAAc,EAAEb,qBAAqB;MACrCD;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAMe,gBAAgB,GAAGA,CAACC,SAAS,EAAEpP,WAAW,KAAK;IACnD,MAAMqP,cAAc,GAAGtB,wBAAwB,CAACqB,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC;;IAEpE;IACA;IACA,IAAIE,SAAS,GAAGD,cAAc,CAACR,SAAS;IACxC,IAAIU,SAAS,GAAGF,cAAc,CAACP,SAAS,GAAG,CAAC,CAAC,CAAC;;IAE9C;IACA,IAAI9O,WAAW,EAAE;MACfsP,SAAS,GAAGD,cAAc,CAACR,SAAS,GAAG,CAAC,CAAC,CAAC;IAC5C;;IAEA;IACA,MAAMW,SAAS,GAAG,GAAG,CAAC,CAAC;IACvB,MAAMC,WAAW,GAAGL,SAAS,CAAClI,WAAW;IACzC,MAAMwI,gBAAgB,GAAGF,SAAS,GAAGC,WAAW;;IAEhD;IACA,QAAQL,SAAS,CAAC/N,IAAI;MACpB,KAAK,YAAY;QACf;QACAkO,SAAS,IAAI,CAAC;QACd;MACF,KAAK,QAAQ;QACX;QACAA,SAAS,IAAI,GAAG;QAChB;MACF,KAAK,OAAO;QACV;QACAA,SAAS,IAAI,GAAG;QAChB;MACF;QACE;IACJ;IAEA,OAAO;MACL,GAAGF,cAAc;MACjBR,SAAS,EAAES,SAAS;MACpBR,SAAS,EAAES,SAAS;MACpBR,KAAK,EAAExF,IAAI,CAACzF,GAAG,CAACuL,cAAc,CAACN,KAAK,EAAEW,gBAAgB,CAAC,CAAC;IAC1D,CAAC;EACH,CAAC;;EAED;;EAEA;EACA,MAAM7D,0BAA0B,GAAGA,CAAA,KAAM;IACvC;IACA,IAAI8D,aAAa,GAAG,CAAC;IACrB,IAAIC,YAAY,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAEjC,MAAMC,eAAe,GAAIC,CAAC,IAAK;MAC7B,MAAMC,QAAQ,GAAG1G,IAAI,CAAC2G,IAAI,CACxB3G,IAAI,CAAC4G,GAAG,CAACH,CAAC,CAACI,OAAO,GAAGR,YAAY,CAACC,CAAC,EAAE,CAAC,CAAC,GACvCtG,IAAI,CAAC4G,GAAG,CAACH,CAAC,CAACK,OAAO,GAAGT,YAAY,CAACE,CAAC,EAAE,CAAC,CACxC,CAAC;MACDH,aAAa,IAAIM,QAAQ;MACzBL,YAAY,GAAG;QAAEC,CAAC,EAAEG,CAAC,CAACI,OAAO;QAAEN,CAAC,EAAEE,CAAC,CAACK;MAAQ,CAAC;MAE7CxO,kBAAkB,CAACiK,IAAI,KAAK;QAC1B,GAAGA,IAAI;QACPxJ,cAAc,EAAEqN;MAClB,CAAC,CAAC,CAAC;IACL,CAAC;;IAED;IACA,MAAMW,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,SAAS,GAAGxF,MAAM,CAACyF,WAAW,IAAI1R,QAAQ,CAAC+N,eAAe,CAAC0D,SAAS;MAC1E,MAAME,SAAS,GAAG3R,QAAQ,CAAC+N,eAAe,CAAC6D,YAAY,GAAG3F,MAAM,CAAC6B,WAAW;MAC5E,MAAM+D,aAAa,GAAGpH,IAAI,CAACqH,KAAK,CAAEL,SAAS,GAAGE,SAAS,GAAI,GAAG,CAAC;MAE/D5O,kBAAkB,CAACiK,IAAI,KAAK;QAC1B,GAAGA,IAAI;QACPzJ,WAAW,EAAEkH,IAAI,CAACxF,GAAG,CAAC+H,IAAI,CAACzJ,WAAW,EAAEsO,aAAa;MACvD,CAAC,CAAC,CAAC;MAEHE,gBAAgB,CAAC,QAAQ,EAAE;QAAEC,KAAK,EAAEH,aAAa;QAAEI,QAAQ,EAAER;MAAU,CAAC,CAAC;IAC3E,CAAC;;IAED;IACA,IAAIS,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC/B,IAAIC,aAAa,GAAG,IAAI;IAExB,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAI,CAACD,aAAa,EAAE;QAClBtP,kBAAkB,CAACiK,IAAI,KAAK;UAC1B,GAAGA,IAAI;UACPzI,gBAAgB,EAAE;YAChB,GAAGyI,IAAI,CAACzI,gBAAgB;YACxBG,YAAY,EAAEsI,IAAI,CAACzI,gBAAgB,CAACG,YAAY,GAAG;UACrD;QACF,CAAC,CAAC,CAAC;QACHwN,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;QAC3BC,aAAa,GAAG,IAAI;MACtB;IACF,CAAC;IAED,MAAME,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAIF,aAAa,EAAE;QACjB,MAAM7N,SAAS,GAAG2N,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,cAAc;QAC7CnP,kBAAkB,CAACiK,IAAI,KAAK;UAC1B,GAAGA,IAAI;UACPzI,gBAAgB,EAAE;YAChB,GAAGyI,IAAI,CAACzI,gBAAgB;YACxBC,SAAS,EAAEwI,IAAI,CAACzI,gBAAgB,CAACC,SAAS,GAAGA,SAAS;YACtDC,UAAU,EAAEuI,IAAI,CAACzI,gBAAgB,CAACE,UAAU,GAAG;UACjD;QACF,CAAC,CAAC,CAAC;QACH4N,aAAa,GAAG,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAMG,gBAAgB,GAAItB,CAAC,IAAK;MAC9B,IAAIA,CAAC,CAACK,OAAO,IAAI,CAAC,EAAE;QAClBxO,kBAAkB,CAACiK,IAAI,KAAK;UAC1B,GAAGA,IAAI;UACP7I,UAAU,EAAE;YACVC,QAAQ,EAAE,IAAI;YACdC,SAAS,EAAE,IAAI8N,IAAI,CAAC,CAAC;YACrB7N,gBAAgB,EAAE,CAAC0B,gBAAgB,CAACQ;UACtC;QACF,CAAC,CAAC,CAAC;QACHuL,gBAAgB,CAAC,aAAa,EAAE;UAAE1N,SAAS,EAAE,IAAI8N,IAAI,CAAC;QAAE,CAAC,CAAC;MAC5D;IACF,CAAC;;IAED;IACA,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,IAAI,QAAQ,IAAIC,WAAW,EAAE;QAC3B,MAAMC,MAAM,GAAGD,WAAW,CAACC,MAAM;QACjC9N,qBAAqB,CAACmI,IAAI,KAAK;UAC7B,GAAGA,IAAI;UACP7H,WAAW,EAAE;YACXC,IAAI,EAAEuN,MAAM,CAACC,cAAc;YAC3BvN,KAAK,EAAEsN,MAAM,CAACE;UAChB;QACF,CAAC,CAAC,CAAC;MACL;;MAEA;MACA,IAAI,YAAY,IAAI5H,SAAS,EAAE;QAC7B,MAAM6H,UAAU,GAAG7H,SAAS,CAAC6H,UAAU;QACvCjO,qBAAqB,CAACmI,IAAI,KAAK;UAC7B,GAAGA,IAAI;UACP1H,cAAc,EAAE;YACdyN,cAAc,EAAED,UAAU,CAACvQ,IAAI;YAC/ByQ,QAAQ,EAAEF,UAAU,CAACE,QAAQ;YAC7BC,GAAG,EAAEH,UAAU,CAACG,GAAG;YACnBC,aAAa,EAAEJ,UAAU,CAACI;UAC5B;QACF,CAAC,CAAC,CAAC;MACL;IACF,CAAC;;IAED;IACAlT,QAAQ,CAACqN,gBAAgB,CAAC,WAAW,EAAE4D,eAAe,CAAC;IACvDhF,MAAM,CAACoB,gBAAgB,CAAC,QAAQ,EAAEmE,YAAY,CAAC;IAC/CvF,MAAM,CAACoB,gBAAgB,CAAC,OAAO,EAAEiF,WAAW,CAAC;IAC7CrG,MAAM,CAACoB,gBAAgB,CAAC,MAAM,EAAEkF,UAAU,CAAC;IAC3CvS,QAAQ,CAACqN,gBAAgB,CAAC,YAAY,EAAEmF,gBAAgB,CAAC;;IAEzD;IACAC,kBAAkB,CAAC,CAAC;IACpB,MAAMU,mBAAmB,GAAGC,WAAW,CAACX,kBAAkB,EAAE,IAAI,CAAC;;IAEjE;IACA,OAAO,MAAM;MACXzS,QAAQ,CAACsN,mBAAmB,CAAC,WAAW,EAAE2D,eAAe,CAAC;MAC1DhF,MAAM,CAACqB,mBAAmB,CAAC,QAAQ,EAAEkE,YAAY,CAAC;MAClDvF,MAAM,CAACqB,mBAAmB,CAAC,OAAO,EAAEgF,WAAW,CAAC;MAChDrG,MAAM,CAACqB,mBAAmB,CAAC,MAAM,EAAEiF,UAAU,CAAC;MAC9CvS,QAAQ,CAACsN,mBAAmB,CAAC,YAAY,EAAEkF,gBAAgB,CAAC;MAC5Da,aAAa,CAACF,mBAAmB,CAAC;IACpC,CAAC;EACH,CAAC;;EAED;EACA,MAAMG,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,mCAAmC,CAAC;MACjE,MAAMtJ,IAAI,GAAG,MAAMqJ,QAAQ,CAACE,IAAI,CAAC,CAAC;MAClC,OAAOvJ,IAAI,CAACwJ,EAAE;IAChB,CAAC,CAAC,OAAO3K,KAAK,EAAE;MACdF,OAAO,CAACG,IAAI,CAAC,wBAAwB,EAAED,KAAK,CAAC;MAC7C,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EACA,MAAM8D,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAAC3K,SAAS,CAACG,MAAM,IAAI,CAACH,SAAS,CAACE,KAAK,EAAE;MACzCyG,OAAO,CAACG,IAAI,CAAC,8CAA8C,CAAC;MAC5D;IACF;IAEA,IAAI;MACF,MAAM2K,iBAAiB,GAAGjB,WAAW,CAACN,GAAG,CAAC,CAAC;MAC3C,MAAMwB,MAAM,GAAG,MAAMN,SAAS,CAAC,CAAC;MAEhC,MAAMO,WAAW,GAAG;QAClBjH,QAAQ,EAAE1K,SAAS,CAACG,MAAM;QAC1ByR,SAAS,EAAE5R,SAAS,CAACE,KAAK;QAC1B2R,WAAW,EAAE,CAAA/S,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+G,IAAI,KAAI,gBAAgB;QACtDiM,eAAe,EAAE9R,SAAS,CAACK,IAAI,IAAI,SAAS;QAC5C0R,MAAM,EAAE;UACN1R,IAAI,EAAE0J,MAAM,CAAC0B,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG1B,MAAM,CAAC0B,UAAU,IAAI,IAAI,GAAG,QAAQ,GAAG,SAAS;UAC5FuG,gBAAgB,EAAE,GAAGjI,MAAM,CAACkI,MAAM,CAACxK,KAAK,IAAIsC,MAAM,CAACkI,MAAM,CAACtK,MAAM,EAAE;UAClEuK,EAAE,EAAEnJ,SAAS,CAACoJ,QAAQ;UACtBC,OAAO,EAAErJ,SAAS,CAACsJ,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;UAC7CF,SAAS,EAAEtJ,SAAS,CAACsJ;QACvB,CAAC;QACDrI,QAAQ,EAAE;UACRwI,QAAQ,EAAEC,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAACC,QAAQ;UAC1DC,QAAQ,EAAE/U,QAAQ,CAAC+U,QAAQ;UAC3BnB,MAAM,EAAEA;QACV,CAAC;QACD9Q,eAAe,EAAE;UACfE,sBAAsB,EAAE,IAAI;UAC5BK,iBAAiB,EAAE,KAAK;UACxBF,oBAAoB,EAAE,KAAK;UAC3BD,wBAAwB,EAAE,KAAK;UAC/BD,eAAe,EAAE,CAAC;UAClB+R,gBAAgB,EAAE,CAAC;YACjBlB,SAAS,EAAE5R,SAAS,CAACE,KAAK;YAC1B6S,QAAQ,EAAE;UACZ,CAAC,CAAC;UACF,GAAGnS;QACL,CAAC;QACD8B,kBAAkB,EAAE;UAClBsQ,YAAY,EAAE,IAAI;UAClBC,gBAAgB,EAAE,EAAE;UACpBC,MAAM,EAAE,EAAE;UACV,GAAGxQ;QACL,CAAC;QACDa,cAAc;QACdO;MACF,CAAC;MAED6C,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE+K,WAAW,CAAC;MAE3D,MAAMwB,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAIvJ,MAAM,CAACC,QAAQ,CAACuJ,MAAM;MACvE,MAAMC,MAAM,GAAG,GAAGL,OAAO,CAACM,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,wBAAwB;MAErE,MAAMpC,QAAQ,GAAG,MAAMC,KAAK,CAACkC,MAAM,EAAE;QACnCE,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACnC,WAAW;MAClC,CAAC,CAAC;MAEF,IAAIN,QAAQ,CAAC0C,EAAE,EAAE;QACf,MAAMC,MAAM,GAAG,MAAM3C,QAAQ,CAACE,IAAI,CAAC,CAAC;QACpChR,YAAY,CAACyT,MAAM,CAAC1T,SAAS,CAAC;QAC9BG,mBAAmB,CAAC,IAAIwP,IAAI,CAAC,CAAC,CAAC;;QAE/B;QACA,MAAM+C,YAAY,GAAGxC,WAAW,CAACN,GAAG,CAAC,CAAC,GAAGuB,iBAAiB;QAC1DwC,sBAAsB,CAAC,cAAc,EAAEjB,YAAY,CAAC;QAEpDrM,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEoN,MAAM,CAAC1T,SAAS,CAAC;MAC7D,CAAC,MAAM;QACL,MAAM4T,SAAS,GAAG,MAAM7C,QAAQ,CAACE,IAAI,CAAC,CAAC;QACvC5K,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEqN,SAAS,CAAC;QAC9DC,UAAU,CAAC,eAAe,EAAED,SAAS,CAACE,OAAO,CAAC;MAChD;IACF,CAAC,CAAC,OAAOvN,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1DsN,UAAU,CAAC,eAAe,EAAEtN,KAAK,CAACuN,OAAO,CAAC;IAC5C;EACF,CAAC;EAED,MAAMvE,gBAAgB,GAAGA,CAACxP,IAAI,EAAE2H,IAAI,GAAG,CAAC,CAAC,KAAK;IAC5C,MAAMqM,WAAW,GAAG;MAClBhU,IAAI;MACJ8B,SAAS,EAAE,IAAI8N,IAAI,CAAC,CAAC;MACrBjI;IACF,CAAC;;IAED;IACA,IAAI3H,IAAI,KAAK,aAAa,EAAE;MAC1BiU,oBAAoB,CAAC,mBAAmB,EAAE,IAAI,CAAC;IACjD,CAAC,MAAM,IAAIjU,IAAI,KAAK,gBAAgB,EAAE;MACpCiU,oBAAoB,CAAC,sBAAsB,EAAE,IAAI,CAAC;IACpD,CAAC,MAAM,IAAIjU,IAAI,KAAK,oBAAoB,EAAE;MACxCiU,oBAAoB,CAAC,0BAA0B,EAAE,IAAI,CAAC;IACxD,CAAC,MAAM,IAAIjU,IAAI,KAAK,gBAAgB,EAAE;MACpCiU,oBAAoB,CAAC,iBAAiB,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IAC7D;;IAEA;IACAuJ,WAAW,CAACtE,QAAQ,GAAG/H,IAAI,CAAC+H,QAAQ,IAAI;MAAElB,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACtDuF,WAAW,CAACE,OAAO,GAAGvM,IAAI,CAACuM,OAAO,IAAI,EAAE;IACxCF,WAAW,CAACtB,QAAQ,GAAG/K,IAAI,CAAC+K,QAAQ,IAAI,CAAC;IACzCsB,WAAW,CAACG,SAAS,GAAGxM,IAAI,CAACwM,SAAS,IAAI,CAAC;IAC3CH,WAAW,CAACI,QAAQ,GAAG/T,YAAY,CAACwH,MAAM,GAAG,CAAC;IAC9CmM,WAAW,CAACK,OAAO,GAAG1M,IAAI,CAAC0M,OAAO,IAAI,EAAE;;IAExC;IACA,IAAIrU,IAAI,KAAK,YAAY,EAAE;MACzBiU,oBAAoB,CAAC,yBAAyB,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;MACnE/G,mBAAmB,CAAC+G,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP5G,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAI7D,IAAI,KAAK,OAAO,EAAE;MAC3BiU,oBAAoB,CAAC,oBAAoB,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;MAC9D/G,mBAAmB,CAAC+G,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP3G,YAAY,EAAE;MAChB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAI9D,IAAI,KAAK,MAAM,EAAE;MAC1BiU,oBAAoB,CAAC,mBAAmB,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IAC/D,CAAC,MAAM,IAAIzK,IAAI,KAAK,iBAAiB,EAAE;MACrCiU,oBAAoB,CAAC,6BAA6B,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IACzE,CAAC,MAAM,IAAIzK,IAAI,KAAK,cAAc,EAAE;MAClCiU,oBAAoB,CAAC,0BAA0B,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IACtE,CAAC,MAAM,IAAIzK,IAAI,KAAK,gBAAgB,EAAE;MACpCiU,oBAAoB,CAAC,8BAA8B,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IAC1E,CAAC,MAAM,IAAIzK,IAAI,KAAK,SAAS,EAAE;MAC7BiU,oBAAoB,CAAC,2BAA2B,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IACvE;;IAEA;IACA,IAAI,CAAClK,eAAe,CAACE,sBAAsB,IAAIN,gBAAgB,EAAE;MAC/D,MAAMM,sBAAsB,GAAG,CAAC,IAAImP,IAAI,CAAC,CAAC,GAAGzP,gBAAgB,IAAI,IAAI;MACrE8T,oBAAoB,CAAC,wBAAwB,EAAExT,sBAAsB,CAAC;IACxE;;IAEA;IACA,MAAMM,eAAe,GAAGmH,IAAI,CAACzF,GAAG,CAAC,GAAG,EACjCpC,YAAY,CAACwH,MAAM,GAAG,CAAC,GACvBtH,eAAe,CAACa,YAAY,CAACK,UAAU,GAAG,EAAG,GAC7ClB,eAAe,CAACa,YAAY,CAACM,KAAK,GAAG,EAAG,GACxCnB,eAAe,CAACS,WAAW,GAAG,GAAI,GAClCT,eAAe,CAACU,cAAc,GAAG,KACpC,CAAC;IAEDT,kBAAkB,CAACiK,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP1J,eAAe,EAAEmH,IAAI,CAACqH,KAAK,CAACxO,eAAe;IAC7C,CAAC,CAAC,CAAC;IAEHT,eAAe,CAACmK,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEuJ,WAAW,CAAC,CAAC;IAC/C1N,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEyN,WAAW,CAAC;EAC3D,CAAC;EAED,MAAMJ,sBAAsB,GAAGA,CAACU,MAAM,EAAEC,KAAK,KAAK;IAChD,MAAMzB,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAIvJ,MAAM,CAACC,QAAQ,CAACuJ,MAAM;IACvE,MAAMC,MAAM,GAAG,GAAGL,OAAO,CAACM,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,0BAA0BnT,SAAS,cAAc;IAE9FgR,KAAK,CAACkC,MAAM,EAAE;MACZE,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBa,MAAM;QACNC,KAAK;QACLzS,SAAS,EAAE,IAAI8N,IAAI,CAAC;MACtB,CAAC;IACH,CAAC,CAAC,CAAC4E,KAAK,CAAChO,KAAK,IAAI;MAChBF,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsN,UAAU,GAAGA,CAAC9T,IAAI,EAAE+T,OAAO,KAAK;IACpC,MAAMjB,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAIvJ,MAAM,CAACC,QAAQ,CAACuJ,MAAM;IACvE,MAAMC,MAAM,GAAG,GAAGL,OAAO,CAACM,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,0BAA0BnT,SAAS,QAAQ;IAExFgR,KAAK,CAACkC,MAAM,EAAE;MACZE,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBzT,IAAI;QACJ+T,OAAO;QACPjS,SAAS,EAAE,IAAI8N,IAAI,CAAC;MACtB,CAAC;IACH,CAAC,CAAC,CAAC4E,KAAK,CAAChO,KAAK,IAAI;MAChBF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyN,oBAAoB,GAAGA,CAACK,MAAM,EAAEC,KAAK,KAAK;IAC9C;IACA/T,kBAAkB,CAACiK,IAAI,IAAI;MACzB,MAAMgK,UAAU,GAAG;QAAE,GAAGhK;MAAK,CAAC;;MAE9B;MACA,IAAI6J,MAAM,CAACI,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxB,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC,GAAGN,MAAM,CAACrC,KAAK,CAAC,GAAG,CAAC;QACzC,IAAIwC,UAAU,CAACE,MAAM,CAAC,EAAE;UACtBF,UAAU,CAACE,MAAM,CAAC,GAAG;YAAE,GAAGF,UAAU,CAACE,MAAM,CAAC;YAAE,CAACC,KAAK,GAAGL;UAAM,CAAC;QAChE;MACF,CAAC,MAAM;QACLE,UAAU,CAACH,MAAM,CAAC,GAAG,OAAOC,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAC9J,IAAI,CAAC6J,MAAM,CAAC,CAAC,GAAGC,KAAK;MAChF;MAEA,OAAOE,UAAU;IACnB,CAAC,CAAC;;IAEF;IACA,IAAIxU,SAAS,EAAE;MACb,MAAM6S,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAIvJ,MAAM,CAACC,QAAQ,CAACuJ,MAAM;MACvE,MAAMC,MAAM,GAAG,GAAGL,OAAO,CAACM,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,0BAA0BnT,SAAS,WAAW;MAE3FgR,KAAK,CAACkC,MAAM,EAAE;QACZE,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBa,MAAM;UACNC,KAAK,EAAE,OAAOA,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAChU,eAAe,CAAC+T,MAAM,CAAC,IAAI,CAAC,CAAC,GAAGC,KAAK;UAChFzS,SAAS,EAAE,IAAI8N,IAAI,CAAC;QACtB,CAAC;MACH,CAAC,CAAC,CAAC4E,KAAK,CAAChO,KAAK,IAAI;QAChBF,OAAO,CAACE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMmE,mBAAmB,GAAG,MAAAA,CAAOkK,OAAO,GAAG,WAAW,KAAK;IAC3D,IAAI,CAAC5U,SAAS,EAAE;IAEhB,IAAI;MACF,MAAM6U,OAAO,GAAG,IAAIlF,IAAI,CAAC,CAAC;MAC1B,MAAM8C,QAAQ,GAAGvS,gBAAgB,GAAG+H,IAAI,CAAC6M,KAAK,CAAC,CAACD,OAAO,GAAG3U,gBAAgB,IAAI,IAAI,CAAC,GAAG,CAAC;MAEvF,MAAM6U,UAAU,GAAG;QACjBF,OAAO;QACPpC,QAAQ;QACRmC,OAAO;QACPxU,YAAY;QACZE,eAAe;QACf8B,kBAAkB;QAClBa,cAAc;QACdO;MACF,CAAC;MAED,MAAMqP,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAIvJ,MAAM,CAACC,QAAQ,CAACuJ,MAAM;MACvE,MAAMC,MAAM,GAAG,GAAGL,OAAO,CAACM,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,0BAA0BnT,SAAS,EAAE;MAElF,MAAM+Q,QAAQ,GAAG,MAAMC,KAAK,CAACkC,MAAM,EAAE;QACnCE,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACuB,UAAU;MACjC,CAAC,CAAC;MAEF,IAAIhE,QAAQ,CAAC0C,EAAE,EAAE;QACfpN,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEsO,OAAO,EAAE,WAAW,EAAEnC,QAAQ,EAAE,SAAS,CAAC;MACpF,CAAC,MAAM;QACL,MAAMmB,SAAS,GAAG,MAAM7C,QAAQ,CAACE,IAAI,CAAC,CAAC;QACvC5K,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEqN,SAAS,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOrN,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D;EACF,CAAC;;EAMD;EACA,MAAMyO,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAAC7W,QAAQ,CAAC4K,OAAO,IAAI,CAAC1K,SAAS,CAAC0K,OAAO,EAAE,OAAO,KAAK;IAEzD,MAAMH,KAAK,GAAGzK,QAAQ,CAAC4K,OAAO;IAC9B,MAAM/B,MAAM,GAAG3I,SAAS,CAAC0K,OAAO;IAChC,MAAM9B,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACAF,MAAM,CAACG,KAAK,GAAGyB,KAAK,CAACQ,UAAU;IAC/BpC,MAAM,CAACK,MAAM,GAAGuB,KAAK,CAACS,WAAW;;IAEjC;IACApC,GAAG,CAACM,SAAS,CAACqB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE5B,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;;IAEvD;IACA,MAAM4N,cAAc,GAAGrM,KAAK,CAACsM,aAAa;IAC1C,MAAMC,aAAa,GAAGF,cAAc,CAACG,qBAAqB,CAAC,CAAC;;IAE5D;IACA,MAAMC,WAAW,GAAGzM,KAAK,CAACQ,UAAU,GAAGR,KAAK,CAACS,WAAW;IACxD,MAAMiM,eAAe,GAAGH,aAAa,CAAChO,KAAK,GAAGgO,aAAa,CAAC9N,MAAM;IAElE,IAAIkO,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAEC,OAAO;IAEjD,IAAIL,WAAW,GAAGC,eAAe,EAAE;MACjC;MACAE,aAAa,GAAGL,aAAa,CAAC9N,MAAM;MACpCkO,YAAY,GAAGC,aAAa,GAAGH,WAAW;MAC1CI,OAAO,GAAG,CAACF,YAAY,GAAGJ,aAAa,CAAChO,KAAK,IAAI,CAAC;MAClDuO,OAAO,GAAG,CAAC;IACb,CAAC,MAAM;MACL;MACAH,YAAY,GAAGJ,aAAa,CAAChO,KAAK;MAClCqO,aAAa,GAAGD,YAAY,GAAGF,WAAW;MAC1CI,OAAO,GAAG,CAAC;MACXC,OAAO,GAAG,CAACF,aAAa,GAAGL,aAAa,CAAC9N,MAAM,IAAI,CAAC;IACtD;;IAEA;IACA;IACA;IACA;;IAEA,MAAMsO,MAAM,GAAG3O,MAAM,CAACG,KAAK,GAAGoO,YAAY;IAC1C,MAAMK,MAAM,GAAG5O,MAAM,CAACK,MAAM,GAAGmO,aAAa;;IAE5C;IACA,MAAMK,KAAK,GAAG5N,IAAI,CAACxF,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAI8S,YAAY,GAAGE,OAAO,IAAIE,MAAM,CAAC;IAC1E,MAAMG,KAAK,GAAG7N,IAAI,CAACxF,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAI+S,aAAa,GAAGE,OAAO,IAAIE,MAAM,CAAC;IAC3E,MAAMG,SAAS,GAAG9N,IAAI,CAACzF,GAAG,CAACwE,MAAM,CAACG,KAAK,GAAG0O,KAAK,EAAI,GAAG,GAAG,GAAG,GAAIN,YAAY,GAAII,MAAM,CAAC;IACvF,MAAMK,UAAU,GAAG/N,IAAI,CAACzF,GAAG,CAACwE,MAAM,CAACK,MAAM,GAAGyO,KAAK,EAAI,GAAG,GAAG,GAAG,GAAIN,aAAa,GAAII,MAAM,CAAC;;IAE1F;IACA,MAAMK,OAAO,GAAGhO,IAAI,CAACxF,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAI8S,YAAY,GAAGE,OAAO,IAAIE,MAAM,CAAC,CAAC,CAAC;IAC9E,MAAMO,OAAO,GAAGjO,IAAI,CAACxF,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAI+S,aAAa,GAAGE,OAAO,IAAIE,MAAM,CAAC,CAAC,CAAC;IAC/E,MAAMO,WAAW,GAAGlO,IAAI,CAACzF,GAAG,CAACwE,MAAM,CAACG,KAAK,GAAG8O,OAAO,EAAI,GAAG,GAAG,GAAG,GAAIV,YAAY,GAAII,MAAM,CAAC,CAAC,CAAC;IAC7F,MAAMS,YAAY,GAAGnO,IAAI,CAACzF,GAAG,CAACwE,MAAM,CAACK,MAAM,GAAG6O,OAAO,EAAI,GAAG,GAAG,GAAG,GAAIV,aAAa,GAAII,MAAM,CAAC,CAAC,CAAC;;IAEhG,IAAI;MACF;MACA,MAAMS,aAAa,GAAGpP,GAAG,CAACQ,YAAY,CAACoO,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,UAAU,CAAC;MAC3E,MAAMM,QAAQ,GAAGD,aAAa,CAAC3O,IAAI;;MAEnC;MACA,MAAM6O,eAAe,GAAGtP,GAAG,CAACQ,YAAY,CAACwO,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,CAAC;MACrF,MAAMI,UAAU,GAAGD,eAAe,CAAC7O,IAAI;MAEvC,IAAI+O,cAAc,GAAG,CAAC;MACtB,IAAIC,eAAe,GAAG,CAAC;MACvB,IAAIC,gBAAgB,GAAG,CAAC;MACxB,IAAIC,iBAAiB,GAAG,CAAC;;MAEzB;MACA,MAAMC,UAAU,GAAGA,CAAChP,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;QAC9B;QACA,MAAM+O,UAAU,GAAGjP,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAID,CAAC,GAAGE,CAAC,IAAIE,IAAI,CAACC,GAAG,CAACL,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;QACvF,MAAMiP,UAAU,GAAGlP,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIE,IAAI,CAACC,GAAG,CAACL,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IAAIG,IAAI,CAACC,GAAG,CAACJ,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;QAChG,MAAMiP,UAAU,GAAGnP,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAIA,CAAC,GAAGC,CAAC;QAChE,MAAMkP,UAAU,GAAGpP,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAIA,CAAC,IAAIC,CAAC;QAElE,OAAO+O,UAAU,IAAIC,UAAU,IAAIC,UAAU,IAAIC,UAAU;MAC7D,CAAC;;MAED;MACA,KAAK,IAAItP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2O,QAAQ,CAAC1O,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC3C,MAAME,CAAC,GAAGyO,QAAQ,CAAC3O,CAAC,CAAC;QACrB,MAAMG,CAAC,GAAGwO,QAAQ,CAAC3O,CAAC,GAAG,CAAC,CAAC;QACzB,MAAMI,CAAC,GAAGuO,QAAQ,CAAC3O,CAAC,GAAG,CAAC,CAAC;QAEzB,IAAIkP,UAAU,CAAChP,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvB0O,cAAc,EAAE;QAClB;QACAC,eAAe,EAAE;MACnB;;MAEA;MACA,KAAK,IAAI/O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6O,UAAU,CAAC5O,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC7C,MAAME,CAAC,GAAG2O,UAAU,CAAC7O,CAAC,CAAC;QACvB,MAAMG,CAAC,GAAG0O,UAAU,CAAC7O,CAAC,GAAG,CAAC,CAAC;QAC3B,MAAMI,CAAC,GAAGyO,UAAU,CAAC7O,CAAC,GAAG,CAAC,CAAC;QAE3B,IAAIkP,UAAU,CAAChP,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvB4O,gBAAgB,EAAE;QACpB;QACAC,iBAAiB,EAAE;MACrB;;MAEA;MACA,MAAMM,aAAa,GAAGT,cAAc,GAAGC,eAAe;MACtD,MAAMS,eAAe,GAAGR,gBAAgB,GAAGC,iBAAiB;;MAE5D;MACA;MACA;MACA,MAAMQ,eAAe,GAAGF,aAAa,GAAG,IAAI;MAC5C,MAAMG,cAAc,GAAGF,eAAe,GAAG,IAAI;MAE7C,OAAOC,eAAe,IAAIC,cAAc;IAE1C,CAAC,CAAC,OAAO9Q,KAAK,EAAE;MACdF,OAAO,CAACG,IAAI,CAAC,uBAAuB,EAAED,KAAK,CAAC;MAC5C,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAM+Q,2BAA2B,GAAGA,CAACC,WAAW,EAAErR,WAAW,KAAK;IAChE;IACAzH,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,MAAMqP,SAAS,GAAGxI,OAAO,CAACkS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjS,IAAI,KAAK+R,WAAW,CAAC;;IAE3D;IACA9L,UAAU,CAAC,MAAM;MACfhN,kBAAkB,CAAC;QACjB+G,IAAI,EAAE+R,WAAW;QACjBzR,QAAQ,EAAE,CAAAgI,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEhI,QAAQ,KAAI,EAAE;QAAE;QACrC4R,UAAU,EAAE5J,SAAS,CAAC;MACxB,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;;EAED;EACA,MAAM6J,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACvZ,gBAAgB,CAAC2K,OAAO,EAAE;IAE/B,IAAI;MACF;MACA,MAAM/B,MAAM,GAAGxJ,QAAQ,CAACG,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMsJ,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;;MAEnC;MACAF,MAAM,CAACG,KAAK,GAAG/I,gBAAgB,CAAC2K,OAAO,CAAC3B,YAAY,IAAI,GAAG;MAC3DJ,MAAM,CAACK,MAAM,GAAGjJ,gBAAgB,CAAC2K,OAAO,CAACzB,aAAa,IAAI,GAAG;;MAE7D;MACAL,GAAG,CAACM,SAAS,CAACnJ,gBAAgB,CAAC2K,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE/B,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;;MAE1E;MACAL,MAAM,CAAC4Q,MAAM,CAAEC,IAAI,IAAK;QACtB,MAAMC,GAAG,GAAG/N,GAAG,CAACgO,eAAe,CAACF,IAAI,CAAC;QACrC,MAAMG,CAAC,GAAGxa,QAAQ,CAACG,aAAa,CAAC,GAAG,CAAC;QACrCqa,CAAC,CAACC,IAAI,GAAGH,GAAG;QACZE,CAAC,CAACE,QAAQ,GAAG,SAASvI,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;QACtCpS,QAAQ,CAAC8V,IAAI,CAACvV,WAAW,CAACia,CAAC,CAAC;QAC5BA,CAAC,CAACG,KAAK,CAAC,CAAC;QACT3a,QAAQ,CAAC8V,IAAI,CAAC8E,WAAW,CAACJ,CAAC,CAAC;QAC5BjO,GAAG,CAACsO,eAAe,CAACP,GAAG,CAAC;;QAExB;QACAvI,gBAAgB,CAAC,YAAY,EAAE;UAC7B1N,SAAS,EAAE,IAAI8N,IAAI,CAAC,CAAC;UACrB2B,SAAS,EAAE5R,SAAS,CAACE,KAAK;UAC1B0Y,eAAe,EAAEpY,gBAAgB,GAAG,CAAC,IAAIyP,IAAI,CAAC,CAAC,GAAGzP,gBAAgB,IAAI,IAAI,GAAG;QAC/E,CAAC,CAAC;;QAEF;QACAuD,mBAAmB,CAAC+G,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACP5G,cAAc,EAAE;QAClB,CAAC,CAAC,CAAC;MACL,CAAC,EAAE,WAAW,CAAC;IACjB,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CsN,UAAU,CAAC,mBAAmB,EAAEtN,KAAK,CAACuN,OAAO,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMyE,UAAU,GAAIC,SAAS,IAAK;IAChCjJ,gBAAgB,CAAC,MAAM,EAAE;MACvBiJ,SAAS;MACT3W,SAAS,EAAE,IAAI8N,IAAI,CAAC,CAAC;MACrBsE,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMwE,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,IAAIhQ,SAAS,CAAChH,KAAK,EAAE;QACnB,MAAMgH,SAAS,CAAChH,KAAK,CAAC;UACpBiX,KAAK,EAAE,8BAA8B;UACrCC,IAAI,EAAE,mCAAmC;UACzCb,GAAG,EAAErO,MAAM,CAACC,QAAQ,CAACuO;QACvB,CAAC,CAAC;QAEF1I,gBAAgB,CAAC,OAAO,EAAE;UACxB6D,MAAM,EAAE,cAAc;UACtBvR,SAAS,EAAE,IAAI8N,IAAI,CAAC;QACtB,CAAC,CAAC;QAEFlM,mBAAmB,CAAC+G,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACP3G,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL;QACA,MAAM4E,SAAS,CAACmQ,SAAS,CAACC,SAAS,CAACpP,MAAM,CAACC,QAAQ,CAACuO,IAAI,CAAC;QACzDa,KAAK,CAAC,2BAA2B,CAAC;QAElCvJ,gBAAgB,CAAC,OAAO,EAAE;UACxB6D,MAAM,EAAE,WAAW;UACnBvR,SAAS,EAAE,IAAI8N,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOpJ,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCsN,UAAU,CAAC,cAAc,EAAEtN,KAAK,CAACuN,OAAO,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMiF,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACza,UAAU,EAAE;MACf;MACA,MAAM0a,cAAc,GAAG,qBAAqB,CAAC,CAAC;;MAE9C,IAAI5a,gBAAgB,CAAC2K,OAAO,EAAE;QAC5B3K,gBAAgB,CAAC2K,OAAO,CAAC3C,GAAG,GAAG4S,cAAc;QAC7C5a,gBAAgB,CAAC2K,OAAO,CAACpC,KAAK,CAACuC,OAAO,GAAG,OAAO;;QAEhD;QACA9K,gBAAgB,CAAC2K,OAAO,CAACkQ,MAAM,GAAG,MAAM;UACtC;UACAC,uBAAuB,CAAC,CAAC;QAC3B,CAAC;MACH;MAEA3a,aAAa,CAAC,IAAI,CAAC;MACnBkB,oBAAoB,CAAC,IAAI,CAAC;MAC1B;MACA,IAAI,CAACC,SAAS,CAACE,KAAK,EAAE;QACpBf,uBAAuB,CAAC,IAAI,CAAC;MAC/B;MACAI,gBAAgB,CAAC,KAAK,CAAC;;MAEvB;MACAsQ,gBAAgB,CAAC,SAAS,EAAE;QAC1B6D,MAAM,EAAE,aAAa;QACrB+F,UAAU,EAAE,CAAC,CAACzZ,SAAS,CAACE,KAAK;QAC7BiC,SAAS,EAAE,IAAI8N,IAAI,CAAC;MACtB,CAAC,CAAC;;MAEF;MACAhR,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMua,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAAC9a,gBAAgB,CAAC2K,OAAO,EAAE;IAE/B,IAAI;MACF;MACA,MAAM/B,MAAM,GAAGxJ,QAAQ,CAACG,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMsJ,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMkS,GAAG,GAAGhb,gBAAgB,CAAC2K,OAAO;MAEpC/B,MAAM,CAACG,KAAK,GAAGiS,GAAG,CAAChS,YAAY,IAAI,GAAG;MACtCJ,MAAM,CAACK,MAAM,GAAG+R,GAAG,CAAC9R,aAAa,IAAI,GAAG;MACxCL,GAAG,CAACM,SAAS,CAAC6R,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEpS,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;;MAErD;MACA;MACA,MAAMgS,YAAY,GAAGC,yBAAyB,CAACtS,MAAM,CAAC;MAEtD,IAAIqS,YAAY,EAAE;QAChBhT,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE+S,YAAY,CAAC;;QAEnE;QACA5P,MAAM,CAAC8P,iBAAiB,GAAGF,YAAY;;QAEvC;QACA9Y,kBAAkB,CAACiK,IAAI,KAAK;UAC1B,GAAGA,IAAI;UACP7J,oBAAoB,EAAE,IAAI;UAC1B6Y,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;;QAEH;QACAjK,gBAAgB,CAAC,gBAAgB,EAAE;UACjC6D,MAAM,EAAE,kBAAkB;UAC1BqG,OAAO,EAAE,IAAI;UACbJ,YAAY,EAAEA,YAAY;UAC1BxX,SAAS,EAAE,IAAI8N,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLtJ,OAAO,CAACG,IAAI,CAAC,+CAA+C,CAAC;MAC/D;IAEF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdF,OAAO,CAACG,IAAI,CAAC,mCAAmC,EAAED,KAAK,CAAC;MACxDsN,UAAU,CAAC,sBAAsB,EAAEtN,KAAK,CAACuN,OAAO,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAMwF,yBAAyB,GAAItS,MAAM,IAAK;IAC5C,IAAI;MACF,MAAMC,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMM,SAAS,GAAGP,GAAG,CAACQ,YAAY,CAAC,CAAC,EAAE,CAAC,EAAET,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;MACrE,MAAMK,IAAI,GAAGF,SAAS,CAACE,IAAI;;MAE3B;MACA;;MAEA;MACA,IAAIgS,MAAM,GAAG,IAAI;MACjB,IAAIC,MAAM,GAAG3S,MAAM,CAACG,KAAK,GAAG,GAAG,CAAC,CAAC;MACjC,IAAIyS,QAAQ,GAAG5S,MAAM,CAACG,KAAK;;MAE3B;MACA,MAAMhC,MAAM,GAAG8C,IAAI,CAAC6M,KAAK,CAAC9N,MAAM,CAACK,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;MAChD,MAAMwS,IAAI,GAAG7S,MAAM,CAACK,MAAM,GAAG,EAAE,CAAC,CAAC;;MAEjC,KAAK,IAAImH,CAAC,GAAGrJ,MAAM,EAAEqJ,CAAC,GAAGqL,IAAI,EAAErL,CAAC,IAAI,CAAC,EAAE;QACrC,IAAIsL,QAAQ,GAAG,IAAI;QACnB,IAAIC,SAAS,GAAG,IAAI;;QAEpB;QACA,KAAK,IAAIxL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvH,MAAM,CAACG,KAAK,EAAEoH,CAAC,EAAE,EAAE;UACrC,MAAMyL,UAAU,GAAG,CAACxL,CAAC,GAAGxH,MAAM,CAACG,KAAK,GAAGoH,CAAC,IAAI,CAAC;UAC7C,MAAM1G,CAAC,GAAGH,IAAI,CAACsS,UAAU,CAAC;UAC1B,MAAMlS,CAAC,GAAGJ,IAAI,CAACsS,UAAU,GAAG,CAAC,CAAC;UAC9B,MAAMjS,CAAC,GAAGL,IAAI,CAACsS,UAAU,GAAG,CAAC,CAAC;UAC9B,MAAMC,KAAK,GAAGvS,IAAI,CAACsS,UAAU,GAAG,CAAC,CAAC;;UAElC;UACA;UACA,MAAME,WAAW,GAAGD,KAAK,GAAG,GAAG,KAAKpS,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,CAAC;UAElE,IAAImS,WAAW,IAAIJ,QAAQ,KAAK,IAAI,EAAE;YACpCA,QAAQ,GAAGvL,CAAC;UACd;UACA,IAAI2L,WAAW,EAAE;YACfH,SAAS,GAAGxL,CAAC;UACf;QACF;;QAEA;QACA,IAAIuL,QAAQ,KAAK,IAAI,IAAIC,SAAS,KAAK,IAAI,EAAE;UAC3C,MAAMI,QAAQ,GAAGJ,SAAS,GAAGD,QAAQ;UAErC,IAAIK,QAAQ,GAAGP,QAAQ,IAAIO,QAAQ,GAAG,EAAE,EAAE;YAAE;YAC1CP,QAAQ,GAAGO,QAAQ;YACnBT,MAAM,GAAGlL,CAAC;YACVmL,MAAM,GAAGG,QAAQ,GAAGK,QAAQ,GAAG,CAAC;UAClC;QACF;MACF;;MAEA;MACA,IAAI,CAACT,MAAM,EAAE;QACXA,MAAM,GAAG1S,MAAM,CAACK,MAAM,GAAG,IAAI,CAAC,CAAC;QAC/BsS,MAAM,GAAG3S,MAAM,CAACG,KAAK,GAAG,GAAG,CAAC,CAAC;QAC7Bd,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACnE;;MAEA;MACA;MACA,MAAM8T,WAAW,GAAG;QAClB7L,CAAC,EAAEoL,MAAM,GAAG,EAAE;QAAE;QAChBnL,CAAC,EAAEkL,MAAM,GAAG,EAAE;QAAE;QAChBvS,KAAK,EAAE,GAAG;QAAE;QACZE,MAAM,EAAE,EAAE;QAAE;QACZgT,OAAO,EAAEV,MAAM;QACfW,OAAO,EAAEZ,MAAM,GAAG,EAAE;QAAE;QACtBa,UAAU,EAAEb,MAAM,GAAG,GAAG,GAAG,GAAG;QAC9Bc,WAAW,EAAE,IAAI,CAAC;MACpB,CAAC;MAEDnU,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE8T,WAAW,CAAC;MAEtE,OAAOA,WAAW;IAEpB,CAAC,CAAC,OAAO7T,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE;MACA,OAAO;QACLgI,CAAC,EAAEvH,MAAM,CAACG,KAAK,GAAG,GAAG,GAAG,EAAE;QAC1BqH,CAAC,EAAExH,MAAM,CAACK,MAAM,GAAG,IAAI,GAAG,EAAE;QAC5BF,KAAK,EAAE,GAAG;QACVE,MAAM,EAAE,EAAE;QACVgT,OAAO,EAAErT,MAAM,CAACG,KAAK,GAAG,GAAG;QAC3BmT,OAAO,EAAEtT,MAAM,CAACK,MAAM,GAAG,IAAI,GAAG,EAAE;QAClCkT,UAAU,EAAE,GAAG;QACfC,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACnc,UAAU,EAAE;MACf,MAAMoc,eAAe,GAAGvR,YAAY,CAAC,CAAC;MACtC,IAAI/K,gBAAgB,CAAC2K,OAAO,IAAI2R,eAAe,EAAE;QAC/Ctc,gBAAgB,CAAC2K,OAAO,CAAC3C,GAAG,GAAGsU,eAAe;QAC9Ctc,gBAAgB,CAAC2K,OAAO,CAACpC,KAAK,CAACuC,OAAO,GAAG,OAAO;MAClD;MACA3K,aAAa,CAAC,IAAI,CAAC;MACnBkB,oBAAoB,CAAC,KAAK,CAAC;MAC3B;MACA,IAAI,CAACC,SAAS,CAACE,KAAK,EAAE;QACpBf,uBAAuB,CAAC,IAAI,CAAC;MAC/B;MACAI,gBAAgB,CAAC,KAAK,CAAC;;MAEvB;MACAsQ,gBAAgB,CAAC,SAAS,EAAE;QAC1B6D,MAAM,EAAE,gBAAgB;QACxB+F,UAAU,EAAE,CAAC,CAACzZ,SAAS,CAACE,KAAK;QAC7BiC,SAAS,EAAE,IAAI8N,IAAI,CAAC;MACtB,CAAC,CAAC;;MAEF;MACA,IAAItR,SAAS,CAAC0K,OAAO,IAAI5K,QAAQ,CAAC4K,OAAO,EAAE;QACzC,MAAM/B,MAAM,GAAG3I,SAAS,CAAC0K,OAAO;QAChC,MAAM9B,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;QACnCF,MAAM,CAACG,KAAK,GAAGhJ,QAAQ,CAAC4K,OAAO,CAACK,UAAU;QAC1CpC,MAAM,CAACK,MAAM,GAAGlJ,QAAQ,CAAC4K,OAAO,CAACM,WAAW;QAC5CpC,GAAG,CAACM,SAAS,CAACpJ,QAAQ,CAAC4K,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;QACrC,MAAMvB,SAAS,GAAGP,GAAG,CAACQ,YAAY,CAAC,CAAC,EAAE,CAAC,EAAET,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;QACrE1I,cAAc,CAAC0J,qBAAqB,CAACb,SAAS,CAAC,CAAC;MAClD;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAAC9H,SAAS,CAACE,KAAK,EAAE;QACpBf,uBAAuB,CAAC,IAAI,CAAC;MAC/B;IACF;EACF,CAAC;;EAED;EACA,MAAM8b,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIvc,gBAAgB,CAAC2K,OAAO,EAAE;MAC5B3K,gBAAgB,CAAC2K,OAAO,CAACpC,KAAK,CAACuC,OAAO,GAAG,MAAM;IACjD;IACA3K,aAAa,CAAC,KAAK,CAAC;IACpBE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,cAAc,CAAC,KAAK,CAAC;IACrBE,uBAAuB,CAAC,KAAK,CAAC;IAC9ByF,qBAAqB,CAAC,KAAK,CAAC;IAC5BrF,gBAAgB,CAAC,IAAI,CAAC;IACtBQ,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMmb,kBAAkB,GAAIC,MAAM,IAAK;IACrC3W,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IACtBE,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBmL,gBAAgB,CAAC,iBAAiB,EAAE;MAClCxP,IAAI,EAAE,eAAe;MACrB8a,MAAM,EAAE,KAAK;MACbC,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAIjb,IAAI,IAAK;IACtCsE,gBAAgB,CAACtE,IAAI,CAAC;IACtByP,gBAAgB,CAAC,iBAAiB,EAAE;MAClCxP,IAAI,EAAE,mBAAmB;MACzBD;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMkb,eAAe,GAAIC,OAAO,IAAK;IACnClc,YAAY,CAACkc,OAAO,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,QAAQ,GAAG,CAAC5W,oBAAoB;IACtCC,uBAAuB,CAAC2W,QAAQ,CAAC;;IAEjC;IACA,IAAI,CAACA,QAAQ,EAAE;MACbrW,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACfE,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMwW,mBAAmB,GAAGA,CAAA,KAAM;IAChC5W,uBAAuB,CAAC,KAAK,CAAC;IAC9BM,oBAAoB,CAAC,KAAK,CAAC;IAC3BJ,YAAY,CAAC,CAAC,CAAC;IACfE,mBAAmB,CAAC,KAAK,CAAC;IAC1BN,qBAAqB,CAAC,KAAK,CAAC;IAC5BF,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IACtB3E,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7B;IACAxB,YAAY,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMod,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAOvc,SAAS,KAAK,SAAS,GAAGwG,OAAO,GAAGS,SAAS;EACtD,CAAC;;EAED;EACA,MAAMuV,mBAAmB,GAAIC,OAAO,IAAK;IACvCjE,2BAA2B,CAACiE,OAAO,CAAC/V,IAAI,EAAE1G,SAAS,CAAC;EACtD,CAAC;;EAED;EACA,MAAM0c,qBAAqB,GAAGA,CAAA,KAAM;IAClCjc,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACAzC,SAAS,CAAC,MAAM;IACd,IAAI,CAACyH,oBAAoB,IAAIjG,UAAU,EAAE;IAEzC,MAAMmd,QAAQ,GAAG7K,WAAW,CAAC,MAAM;MACjC,MAAMyG,cAAc,GAAGrC,oBAAoB,CAAC,CAAC;MAC7CpQ,mBAAmB,CAACyS,cAAc,CAAC;;MAEnC;MACA,IAAIA,cAAc,IAAI,CAACxS,iBAAiB,IAAIJ,SAAS,KAAK,CAAC,EAAE;QAC3DK,oBAAoB,CAAC,IAAI,CAAC;MAC5B;;MAEA;MACA,IAAI,CAACuS,cAAc,IAAIxS,iBAAiB,EAAE;QACxCC,oBAAoB,CAAC,KAAK,CAAC;QAC3BJ,YAAY,CAAC,CAAC,CAAC;MACjB;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMmM,aAAa,CAAC4K,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAClX,oBAAoB,EAAEjG,UAAU,EAAEuG,iBAAiB,EAAEJ,SAAS,CAAC,CAAC;;EAEpE;EACA3H,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+H,iBAAiB,IAAIvG,UAAU,EAAE;MACpCoG,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACA,IAAI,CAACC,gBAAgB,EAAE;MACrBG,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACAA,YAAY,CAAC,CAAC,CAAC;IAEf,MAAMgX,iBAAiB,GAAG9K,WAAW,CAAC,MAAM;MAC1ClM,YAAY,CAAC8F,IAAI,IAAI;QACnB;QACA,IAAI,CAAC7F,gBAAgB,EAAE;UACrBkM,aAAa,CAAC6K,iBAAiB,CAAC;UAChC5W,oBAAoB,CAAC,KAAK,CAAC;UAC3B,OAAO,CAAC;QACV;QAEA,IAAI0F,IAAI,IAAI,CAAC,EAAE;UACb;UACAqG,aAAa,CAAC6K,iBAAiB,CAAC;UAChC5W,oBAAoB,CAAC,KAAK,CAAC;UAC3BN,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;UAChCiW,aAAa,CAAC,CAAC;UACf,OAAO,CAAC;QACV;QACA,OAAOjQ,IAAI,GAAG,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMqG,aAAa,CAAC6K,iBAAiB,CAAC;EAC/C,CAAC,EAAE,CAAC7W,iBAAiB,EAAEvG,UAAU,EAAEqG,gBAAgB,CAAC,CAAC;;EAErD;EACA7H,SAAS,CAAC,MAAM;IACd,IAAI,CAACyH,oBAAoB,IAAIjG,UAAU,EAAE;IAEzC,MAAMmd,QAAQ,GAAG7K,WAAW,CAAC,MAAM;MACjC,MAAMyG,cAAc,GAAGrC,oBAAoB,CAAC,CAAC;MAC7CpQ,mBAAmB,CAACyS,cAAc,CAAC;;MAEnC;MACA,IAAIA,cAAc,IAAI,CAACxS,iBAAiB,IAAIJ,SAAS,KAAK,CAAC,EAAE;QAC3DK,oBAAoB,CAAC,IAAI,CAAC;MAC5B;;MAEA;MACA,IAAI,CAACuS,cAAc,IAAIxS,iBAAiB,EAAE;QACxCC,oBAAoB,CAAC,KAAK,CAAC;QAC3BJ,YAAY,CAAC,CAAC,CAAC;MACjB;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMmM,aAAa,CAAC4K,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAClX,oBAAoB,EAAEjG,UAAU,EAAEuG,iBAAiB,EAAEJ,SAAS,CAAC,CAAC;;EAEpE;EACA3H,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+H,iBAAiB,IAAIvG,UAAU,EAAE;MACpCoG,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACA,IAAI,CAACC,gBAAgB,EAAE;MACrBG,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACAA,YAAY,CAAC,CAAC,CAAC;IAEf,MAAMgX,iBAAiB,GAAG9K,WAAW,CAAC,MAAM;MAC1ClM,YAAY,CAAC8F,IAAI,IAAI;QACnB;QACA,IAAI,CAAC7F,gBAAgB,EAAE;UACrBkM,aAAa,CAAC6K,iBAAiB,CAAC;UAChC5W,oBAAoB,CAAC,KAAK,CAAC;UAC3B,OAAO,CAAC;QACV;QAEA,IAAI0F,IAAI,IAAI,CAAC,EAAE;UACb;UACAqG,aAAa,CAAC6K,iBAAiB,CAAC;UAChC5W,oBAAoB,CAAC,KAAK,CAAC;UAC3BN,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;UAChCiW,aAAa,CAAC,CAAC;UACf,OAAO,CAAC;QACV;QACA,OAAOjQ,IAAI,GAAG,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMqG,aAAa,CAAC6K,iBAAiB,CAAC;EAC/C,CAAC,EAAE,CAAC7W,iBAAiB,EAAEvG,UAAU,EAAEqG,gBAAgB,CAAC,CAAC;;EAIrD;EACA,MAAMgX,gBAAgB,GAAIjN,CAAC,IAAK;IAC9BxJ,aAAa,CAAC,IAAI,CAAC;IACnBE,SAAS,CAACsJ,CAAC,CAACkN,OAAO,CAAC,CAAC,CAAC,CAAC7M,OAAO,CAAC;EACjC,CAAC;EAED,MAAM8M,eAAe,GAAInN,CAAC,IAAK;IAC7B,IAAI,CAACzJ,UAAU,EAAE;IAEjB,MAAM6W,QAAQ,GAAGpN,CAAC,CAACkN,OAAO,CAAC,CAAC,CAAC,CAAC7M,OAAO;IACrC,MAAMgN,IAAI,GAAGD,QAAQ,GAAG3W,MAAM;;IAE9B;IACA,IAAI4W,IAAI,GAAG,CAAC,EAAE;MACZ/W,gBAAgB,CAAC+W,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAAC/W,UAAU,EAAE;IAEjBC,aAAa,CAAC,KAAK,CAAC;;IAEpB;IACA,IAAIH,aAAa,GAAG,GAAG,EAAE;MACvBlG,uBAAuB,CAAC,KAAK,CAAC;IAChC;;IAEA;IACAmG,gBAAgB,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACAlI,SAAS,CAAC,MAAM;IACd,MAAMmf,kBAAkB,GAAIvN,CAAC,IAAK;MAChC,IAAIrK,kBAAkB,IAAI,CAACqK,CAAC,CAACwN,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC7D7X,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC;IAED9G,QAAQ,CAACqN,gBAAgB,CAAC,WAAW,EAAEoR,kBAAkB,CAAC;IAC1Dze,QAAQ,CAACqN,gBAAgB,CAAC,YAAY,EAAEoR,kBAAkB,CAAC;IAE3D,OAAO,MAAM;MACXze,QAAQ,CAACsN,mBAAmB,CAAC,WAAW,EAAEmR,kBAAkB,CAAC;MAC7Dze,QAAQ,CAACsN,mBAAmB,CAAC,YAAY,EAAEmR,kBAAkB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,CAAC5X,kBAAkB,CAAC,CAAC;;EAExB;EACA,MAAM+X,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,UAAU,GAAGxJ,OAAO,CAACC,GAAG,CAACwJ,qBAAqB,IAAI,sBAAsB;MAC9E,MAAM1J,OAAO,GAAG,GAAGyJ,UAAU,QAAQ;;MAErC;MACA,IAAI5c,SAAS,CAACE,KAAK,IAAIF,SAAS,CAACG,MAAM,IAAIH,SAAS,CAACI,IAAI,IAAIJ,SAAS,CAACK,IAAI,EAAE;QAC3E,MAAM6J,MAAM,GAAG,IAAIJ,eAAe,CAAC,CAAC;QACpC,IAAI9J,SAAS,CAACE,KAAK,EAAEgK,MAAM,CAAC4S,MAAM,CAAC,OAAO,EAAE9c,SAAS,CAACE,KAAK,CAAC;QAC5D,IAAIF,SAAS,CAACG,MAAM,EAAE+J,MAAM,CAAC4S,MAAM,CAAC,QAAQ,EAAE9c,SAAS,CAACG,MAAM,CAAC;QAC/D,IAAIH,SAAS,CAACI,IAAI,EAAE8J,MAAM,CAAC4S,MAAM,CAAC,MAAM,EAAE9c,SAAS,CAACI,IAAI,CAAC;QACzD,IAAIJ,SAAS,CAACK,IAAI,EAAE6J,MAAM,CAAC4S,MAAM,CAAC,MAAM,EAAE9c,SAAS,CAACK,IAAI,CAAC;QACzD,OAAO,GAAG8S,OAAO,IAAIjJ,MAAM,CAAC6S,QAAQ,CAAC,CAAC,EAAE;MAC1C;;MAEA;MACA,OAAO5J,OAAO;IAChB,CAAC;IAED,MAAM6J,OAAO,GAAGL,eAAe,CAAC,CAAC;IAEjC,oBACEjf,OAAA;MAAKuJ,KAAK,EAAEgW,MAAM,CAACC,gBAAiB;MAAAC,QAAA,eAClCzf,OAAA;QAAKuJ,KAAK,EAAEgW,MAAM,CAACG,WAAY;QAAAD,QAAA,gBAC7Bzf,OAAA;UAAIuJ,KAAK,EAAEgW,MAAM,CAACI,OAAQ;UAAAF,QAAA,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtD/f,OAAA;UAAGuJ,KAAK,EAAEgW,MAAM,CAACS,UAAW;UAAAP,QAAA,EACzBnd,SAAS,CAACE,KAAK,GACZ,4DAA4D,GAC5D;QAA+E;UAAAod,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAElF,CAAC,eACJ/f,OAAA;UAAKuJ,KAAK,EAAEgW,MAAM,CAACU,SAAU;UAAAR,QAAA,eAC3Bzf,OAAA,CAACJ,SAAS;YACRsX,KAAK,EAAEoI,OAAQ;YACf5c,IAAI,EAAE,GAAI;YACVwd,KAAK,EAAC,GAAG;YACTC,OAAO,EAAC,SAAS;YACjBC,OAAO,EAAC;UAAS;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN/f,OAAA;UAAGuJ,KAAK,EAAEgW,MAAM,CAACc,MAAO;UAAAZ,QAAA,EAAEH;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACrCzd,SAAS,CAACG,MAAM,iBACfzC,OAAA;UAAGuJ,KAAK,EAAEgW,MAAM,CAACe,UAAW;UAAAb,QAAA,GAAC,UAAQ,EAACnd,SAAS,CAACG,MAAM;QAAA;UAAAmd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAC3D,eACD/f,OAAA;UACEuJ,KAAK,EAAEgW,MAAM,CAACgB,OAAQ;UACtBC,OAAO,EAAE3f,YAAa;UACtB,cAAW,MAAM;UAAA4e,QAAA,EAClB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,IAAI/d,SAAS,EAAE;IACb,oBAAOhC,OAAA,CAACgf,aAAa;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1B;;EAEA;EACA,oBACE/f,OAAA;IAAKuJ,KAAK,EAAEgW,MAAM,CAACkB,SAAU;IAAAhB,QAAA,GAE1Bvd,mBAAmB,iBAClBlC,OAAA;MAAKuJ,KAAK,EAAEgW,MAAM,CAACmB,iBAAkB;MAAAjB,QAAA,eACnCzf,OAAA;QAAKuJ,KAAK,EAAEgW,MAAM,CAACoB,eAAgB;QAAAlB,QAAA,eACjCzf,OAAA;UAAKuJ,KAAK,EAAEgW,MAAM,CAACqB,iBAAkB;UAAAnB,QAAA,gBACnCzf,OAAA;YAAIuJ,KAAK,EAAEgW,MAAM,CAACsB,eAAgB;YAAApB,QAAA,EAAC;UAAgB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxD/f,OAAA;YAAKuJ,KAAK,EAAEgW,MAAM,CAACuB,gBAAiB;YAAArB,QAAA,gBAClCzf,OAAA;cAAGuJ,KAAK,EAAEgW,MAAM,CAACwB,eAAgB;cAAAtB,QAAA,EAAC;YAElC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ/f,OAAA;cAAGuJ,KAAK,EAAEgW,MAAM,CAACwB,eAAgB;cAAAtB,QAAA,EAAC;YAElC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN/f,OAAA;YACEuJ,KAAK,EAAEgW,MAAM,CAACyB,gBAAiB;YAC/BR,OAAO,EAAEpC,qBAAsB;YAAAqB,QAAA,EAChC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD/f,OAAA;MAAKuJ,KAAK,EAAEgW,MAAM,CAAC0B,0BAA2B;MAAAxB,QAAA,gBAC5Czf,OAAA;QACEgJ,GAAG,EAAC,qBAAqB;QACzBkY,GAAG,EAAC,UAAU;QACd3X,KAAK,EAAEgW,MAAM,CAAC4B,qBAAsB;QACpCC,OAAO,EAAG9P,CAAC,IAAK;UACdA,CAAC,CAACwN,MAAM,CAACvV,KAAK,CAACuC,OAAO,GAAG,MAAM;QACjC;MAAE;QAAA8T,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACF/f,OAAA;QAAKuJ,KAAK,EAAEgW,MAAM,CAAC8B,mBAAoB;QAAA5B,QAAA,gBACrCzf,OAAA;UAAMuJ,KAAK,EAAE;YACX,GAAGgW,MAAM,CAAC+B,aAAa;YACvBC,KAAK,EAAEnf,iBAAiB,GAAG,SAAS,GAAGmd,MAAM,CAAC+B,aAAa,CAACC;UAC9D,CAAE;UAAA9B,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpB/f,OAAA;UAAMuJ,KAAK,EAAE;YACX,GAAGgW,MAAM,CAACiC,YAAY;YACtBD,KAAK,EAAEnf,iBAAiB,GAAG,SAAS,GAAGmd,MAAM,CAACiC,YAAY,CAACD;UAC7D,CAAE;UAAA9B,QAAA,EAAC;QAAQ;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/f,OAAA;MAAKuJ,KAAK,EAAEgW,MAAM,CAACkC,eAAgB;MAAAhC,QAAA,gBACjCzf,OAAA;QACE0hB,GAAG,EAAE3gB,QAAS;QACdwI,KAAK,EAAEgW,MAAM,CAACoC,UAAW;QACzBC,QAAQ;QACRC,WAAW;QACXC,KAAK;MAAA;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACF/f,OAAA;QAAQ0hB,GAAG,EAAEzgB,SAAU;QAACsI,KAAK,EAAE;UAAEuC,OAAO,EAAE;QAAO;MAAE;QAAA8T,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtD/f,OAAA;QACE0hB,GAAG,EAAE1gB,gBAAiB;QACtBuI,KAAK,EAAEgW,MAAM,CAACwC,aAAc;QAC5Bb,GAAG,EAAC;MAAe;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,EAGD,CAAC7e,UAAU,iBACVlB,OAAA;QAAKuJ,KAAK,EAAEgW,MAAM,CAACyC,iBAAkB;QAAAvC,QAAA,gBACnCzf,OAAA;UAAMuJ,KAAK,EAAEgW,MAAM,CAAC0C,gBAAiB;UAAAxC,QAAA,EAAC;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjD/f,OAAA;UAAOkiB,SAAS,EAAC,kBAAkB;UAAAzC,QAAA,gBACjCzf,OAAA;YACE2C,IAAI,EAAC,UAAU;YACfwf,OAAO,EAAEhb,oBAAqB;YAC9Bib,QAAQ,EAAEtE,uBAAwB;YAClCuE,QAAQ,EAAE5a,iBAAkB;YAC5B,cAAW;UAAqB;YAAAmY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACF/f,OAAA;YAAMkiB,SAAS,EAAC;UAAe;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,EAGAtY,iBAAiB,iBAChBzH,OAAA;QAAKuJ,KAAK,EAAEgW,MAAM,CAAC+C,gBAAiB;QAAA7C,QAAA,gBAClCzf,OAAA;UAAKuJ,KAAK,EAAEgW,MAAM,CAACgD,eAAgB;UAAA9C,QAAA,EAAEpY;QAAS;UAAAuY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrD/f,OAAA;UAAKuJ,KAAK,EAAEgW,MAAM,CAACiD,aAAc;UAAA/C,QAAA,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN,EAGA,CAAC7e,UAAU,IAAI,CAACuG,iBAAiB,IAAI,CAACN,oBAAoB,iBACzDnH,OAAA;QAAKuJ,KAAK,EAAEgW,MAAM,CAACkD,oBAAqB;QAAAhD,QAAA,eACtCzf,OAAA;UAAKuJ,KAAK,EAAEgW,MAAM,CAACmD,eAAgB;UAAAjD,QAAA,EAAC;QAEpC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5Y,oBAAoB,IAAI,CAACI,gBAAgB,IAAI,CAACE,iBAAiB,iBAC9DzH,OAAA;QAAKuJ,KAAK,EAAEgW,MAAM,CAACoD,kBAAmB;QAAAlD,QAAA,gBACpCzf,OAAA;UAAKuJ,KAAK,EAAEgW,MAAM,CAACqD,eAAgB;UAAAnD,QAAA,EAAC;QAA6C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvF/f,OAAA;UAAKuJ,KAAK,EAAEgW,MAAM,CAACsD,kBAAmB;UAAApD,QAAA,EAAC;QAAgD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1F,CACN,EAEA5Y,oBAAoB,IAAII,gBAAgB,IAAI,CAACE,iBAAiB,iBAC7DzH,OAAA;QAAKuJ,KAAK,EAAEgW,MAAM,CAACuD,aAAc;QAAArD,QAAA,eAC/Bzf,OAAA;UAAKuJ,KAAK,EAAE;YAAC,GAAGgW,MAAM,CAACwD,UAAU;YAAEC,eAAe,EAAE;UAAyB,CAAE;UAAAvD,QAAA,EAAC;QAEhF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAne,aAAa,iBACZ5B,OAAA;QACEuJ,KAAK,EAAE;UACL,GAAGgW,MAAM,CAAC0D,SAAS;UACnBvZ,OAAO,EAAEvC,oBAAoB,IAAII,gBAAgB,GAAG,GAAG,GAAG,GAAG;UAC7DiC,MAAM,EAAErC,oBAAoB,IAAII,gBAAgB,GAC5C,gDAAgD,GAChDJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,iDAAiD,GACjD;QACR,CAAE;QACF2a,SAAS,EAAEpgB,QAAQ,GAAG,mBAAmB,GAAG,EAAG;QAC/C,eAAY,MAAM;QAAA2d,QAAA,eAElBzf,OAAA;UAAKkjB,OAAO,EAAC,aAAa;UAACC,KAAK,EAAC,4BAA4B;UAAA1D,QAAA,gBAE3Dzf,OAAA;YACEojB,CAAC,EAAC,4EAA4E;YAC9EC,MAAM,EACJlc,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD+b,WAAW,EAAC,GAAG;YACfC,IAAI,EAAC,MAAM;YACXC,aAAa,EAAC;UAAO;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACF/f,OAAA;YACEojB,CAAC,EAAC,4EAA4E;YAC9EC,MAAM,EACJlc,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD+b,WAAW,EAAC,GAAG;YACfC,IAAI,EAAC,MAAM;YACXC,aAAa,EAAC;UAAO;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAEF/f,OAAA;YACEmR,CAAC,EAAC,KAAK;YACPC,CAAC,EAAC,KAAK;YACPrH,KAAK,EAAC,KAAK;YACXE,MAAM,EAAC,KAAK;YACZsZ,IAAI,EACFpc,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDmC,OAAO,EAAEvC,oBAAoB,IAAII,gBAAgB,GAAG,MAAM,GAAG,MAAO;YACpEkc,EAAE,EAAC,IAAI;YACPJ,MAAM,EACJlc,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD+b,WAAW,EAAC;UAAG;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAEF/f,OAAA;YACE0jB,EAAE,EAAC,KAAK;YACRC,EAAE,EAAC,KAAK;YACRlZ,CAAC,EAAC,IAAI;YACN8Y,IAAI,EACFpc,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDmC,OAAO,EAAEvC,oBAAoB,IAAII,gBAAgB,GAAG,KAAK,GAAG,KAAM;YAClE8b,MAAM,EACJlc,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD+b,WAAW,EAAC;UAAG;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EAED5Y,oBAAoB,iBACnBnH,OAAA,CAAAE,SAAA;YAAAuf,QAAA,gBACEzf,OAAA;cAAMmR,CAAC,EAAC,KAAK;cAACC,CAAC,EAAC,KAAK;cAACwS,UAAU,EAAC,QAAQ;cAACL,IAAI,EAAC,OAAO;cAACM,QAAQ,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAAArE,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP/f,OAAA;cAAMmR,CAAC,EAAC,KAAK;cAACC,CAAC,EAAC,KAAK;cAACwS,UAAU,EAAC,QAAQ;cAACL,IAAI,EAAC,OAAO;cAACM,QAAQ,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAAArE,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA3e,eAAe,IAAIF,UAAU,iBAC5BlB,OAAA;QAAKuJ,KAAK,EAAE;UACV,GAAGgW,MAAM,CAACwE,eAAe;UACzB;UACAC,GAAG,EAAE5hB,iBAAiB,GAAG,KAAK,GAAG,KAAK;UACtC6hB,SAAS,EAAE7hB,iBAAiB,GAAG,kCAAkC,GAAG,uBAAuB;UAC3F2H,KAAK,EAAErI,SAAS,KAAK,SAAS,GAAG,GAAGuN,WAAW,GAAG,GAAG,GAAGC,cAAc,GAAG;UACzEjF,MAAM,EAAEvI,SAAS,KAAK,SAAS,GAC3B,CAAC,MAAM;YACL,MAAM+N,gBAAgB,GAAGnB,kBAAkB;YAC3C,MAAM4V,YAAY,GAAInd,aAAa,IAAIuH,kBAAmB;YAE1D,IAAI4V,YAAY,EAAE;cAChB;cACA,MAAMC,YAAY,GAAG,CAACpd,aAAa,GAAGuH,kBAAkB,IAAIA,kBAAkB;cAC9E,OAAO,GAAGa,YAAY,IAAI,CAAC,GAAGgV,YAAY,GAAG,GAAG,CAAC,GAAG;YACtD;YACA,OAAO,GAAGhV,YAAY,GAAG;UAC3B,CAAC,EAAE,CAAC,GACJ,GAAGC,eAAe,GAAG;UACzB;UACAgV,QAAQ,EAAE,CAAC,MAAM;YACf,MAAMF,YAAY,GAAInd,aAAa,IAAIuH,kBAAmB;YAC1D,OAAO5M,SAAS,KAAK,SAAS,IAAIwiB,YAAY,GAC1C,gCAAgC,GAChCxiB,SAAS,KAAK,SAAS,IAAIqF,aAAa,GAAGuH,kBAAkB,GAC3D,gCAAgC,GAChC,MAAM;UACd,CAAC,EAAE,CAAC;UACJ+V,QAAQ,EAAE;QACZ,CAAE;QAAA5E,QAAA,eACAzf,OAAA;UAAKuJ,KAAK,EAAE;YACV8I,QAAQ,EAAE,UAAU;YACpBtI,KAAK,EAAE,MAAM;YACbE,MAAM,EAAE,MAAM;YACd6B,OAAO,EAAE,MAAM;YACfwY,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAA9E,QAAA,gBACAzf,OAAA;YACEgJ,GAAG,EAAE,OAAO5H,eAAe,KAAK,QAAQ,GAAGA,eAAe,CAACgH,IAAI,GAAGhH,eAAgB;YAClF8f,GAAG,EAAC,kBAAkB;YACtB3X,KAAK,EAAE;cACLQ,KAAK,EAAE,MAAM;cACbE,MAAM,EAAE,MAAM;cACdua,SAAS,EAAE,SAAS;cACpBP,SAAS,EAAEviB,SAAS,KAAK,WAAW,GAChC,CAAC,MAAM;gBACL;gBACA,MAAM+iB,aAAa,GAAG,uBAAuBrV,eAAe,GAAG,EAAE,GAAG;;gBAEpE;gBACA;gBACA,IAAI9N,WAAW,EAAE;kBACf;kBACA,OAAO,GAAGmjB,aAAa,wBAAwB;gBACjD,CAAC,MAAM;kBACL;kBACA,OAAO,GAAGA,aAAa,aAAa;gBACtC;cACF,CAAC,EAAE,CAAC,GACJ,CAACC,qBAAA,IAAM;gBACL,MAAMjV,gBAAgB,GAAGf,mBAAmB,CAAC7H,UAAU,CAAC;gBACxD,MAAM6I,iBAAiB,GAAG7E,IAAI,CAACxF,GAAG,CAAC0B,aAAa,GAAG6H,iBAAiB,EAAEC,uBAAuB,CAAC;gBAC9F,MAAMqV,YAAY,GAAIrd,UAAU,KAAK,KAAK,IAAI6I,iBAAiB,IAAI,EAAG;gBAEtE,IAAIwU,YAAY,EAAE;kBAChB;kBACA,MAAMC,YAAY,GAAG,CAACzU,iBAAiB,GAAGD,gBAAgB,IAAIA,gBAAgB;kBAC9E,MAAMkV,WAAW,GAAG,CAAC,GAAIR,YAAY,GAAG,GAAI,CAAC,CAAC;kBAC9C,MAAMS,UAAU,GAAGnV,gBAAgB,GAAGC,iBAAiB,CAAC,CAAC;;kBAEzD,OAAO,SAAUP,YAAY,GAAG,EAAE,GAAIyV,UAAU,YAAYA,UAAU,YAAYD,WAAW,GAAG;gBAClG;;gBAEA;gBACA,OAAO,SAAS9Z,IAAI,CAACzF,GAAG,CACrB+J,YAAY,GAAG,EAAE,IAAKO,iBAAiB,GAAGD,gBAAgB,GACvDA,gBAAgB,GAAGC,iBAAiB,GACpCD,gBAAgB,GAAGC,iBAAiB,CAAC,EACzC,GAAG,IAAI,EAAAgV,qBAAA,GAAAtjB,eAAe,CAACkZ,UAAU,cAAAoK,qBAAA,uBAA1BA,qBAAA,CAA4Blc,WAAW,KAAI,EAAE,CAAC,CAAC;gBACxD,CAAC,YAAYkH,iBAAiB,GAAGD,gBAAgB,GAC7CA,gBAAgB,GAAGC,iBAAiB,GACpC,CAAC,GAAG;cACV,CAAC,EAAE,CAAC;cACRlG,MAAM,EAAE;YACV,CAAE;YACFqb,MAAM,EAAGvT,CAAC,IAAK1I,gBAAgB,CAAC0I,CAAC,CAACwN,MAAM,EAAGxc,SAAS,CAACK,IAAI,KAAK,WAAW,IAAIjB,SAAS,KAAK,WAAW,GAAI,UAAU,GAAG,OAAO;UAAE;YAAAke,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjI,CAAC,EACD,CAAEzd,SAAS,CAACK,IAAI,KAAK,WAAW,IAAIjB,SAAS,KAAK,SAAS,IAAM,CAACY,SAAS,CAACK,IAAI,IAAIjB,SAAS,KAAK,SAAU,KAAK,OAAON,eAAe,KAAK,QAAQ,iBACnJpB,OAAA;YAAKuJ,KAAK,EAAE;cACV8I,QAAQ,EAAE,UAAU;cACpByS,MAAM,EAAE,OAAO;cACfC,IAAI,EAAE,KAAK;cACXd,SAAS,EAAE,kBAAkB;cAC7BJ,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBvC,KAAK,EAAE,OAAO;cACdyB,eAAe,EAAE,yBAAyB;cAC1CgC,OAAO,EAAE,SAAS;cAClBC,YAAY,EAAE,MAAM;cACpBC,UAAU,EAAE,QAAQ;cACpBC,aAAa,EAAE,MAAM;cACrBC,SAAS,EAAE,2BAA2B;cACtCC,MAAM,EAAE;YACV,CAAE;YAAA5F,QAAA,GACCre,eAAe,CAACsH,QAAQ,IAAItH,eAAe,CAACiH,YAAY,IAAI/F,SAAS,CAACI,IAAI,IAAI,IAAI,EAAC,IACpF,EAACqE,aAAa,KAAKuH,kBAAkB,iBACnCtO,OAAA;cAAMuJ,KAAK,EAAE;gBACXsa,QAAQ,EAAE,MAAM;gBAChBna,OAAO,EAAE,GAAG;gBACZ4b,UAAU,EAAE;cACd,CAAE;cAAA7F,QAAA,EACC,CAAC,MAAM;gBACN,MAAMjP,cAAc,GAAGlC,kBAAkB,GAAGvH,aAAa;gBAEzD,IAAIwe,iBAAiB;gBACrB,IAAIxe,aAAa,GAAGuH,kBAAkB,EAAE;kBACtC;kBACA,MAAMkX,cAAc,GAAGlX,kBAAkB,GAAGvH,aAAa;kBACzD,MAAM0e,iBAAiB,GAAGnX,kBAAkB,GAAG,IAAI;kBACnD,MAAMoX,iBAAiB,GAAG7a,IAAI,CAACzF,GAAG,CAACogB,cAAc,EAAEC,iBAAiB,CAAC;kBACrE,MAAME,mBAAmB,GAAG,CAAC,GAAID,iBAAiB,GAAGpX,kBAAkB,GAAI,GAAG;kBAC9EiX,iBAAiB,GAAG,CAAC,CAACI,mBAAmB,GAAG,CAAC,IAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;gBAClE,CAAC,MAAM;kBACL;kBACAL,iBAAiB,GAAG,CAAC,CAAC/U,cAAc,GAAG,CAAC,IAAI,GAAG,EAAEoV,OAAO,CAAC,CAAC,CAAC;gBAC7D;;gBAEA;gBACA,MAAMC,WAAW,GAAG9e,aAAa,IAAIuH,kBAAkB,GAAG,KAAK,GAAG,EAAE;gBACpE,OAAO,IAAIvH,aAAa,GAAGuH,kBAAkB,GAAG,GAAG,GAAG,EAAE,GAAGiX,iBAAiB,KAAKM,WAAW,EAAE;cAChG,CAAC,EAAE;YAAC;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EACA,CAACzd,SAAS,CAACK,IAAI,KAAK,WAAW,IAAIjB,SAAS,KAAK,WAAW,KAAK,OAAON,eAAe,KAAK,QAAQ,iBACnGpB,OAAA;YAAKuJ,KAAK,EAAE;cACV8I,QAAQ,EAAE,UAAU;cACpByS,MAAM,EAAE,OAAO;cACfC,IAAI,EAAE,KAAK;cACXd,SAAS,EAAE,kBAAkB;cAC7BJ,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBvC,KAAK,EAAE,OAAO;cACdyB,eAAe,EAAE,yBAAyB;cAC1CgC,OAAO,EAAE,SAAS;cAClBC,YAAY,EAAE,MAAM;cACpBC,UAAU,EAAE,QAAQ;cACpBC,aAAa,EAAE,MAAM;cACrBC,SAAS,EAAE,2BAA2B;cACtCC,MAAM,EAAE;YACV,CAAE;YAAA5F,QAAA,GACCre,eAAe,CAAC2L,aAAa,IAAIzK,SAAS,CAACI,IAAI,IAAI,IAAI,EAAC,UAC3D;UAAA;YAAAkd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAAC7e,UAAU,iBACVlB,OAAA;QAAKuJ,KAAK,EAAEgW,MAAM,CAACuG,sBAAuB;QAAArG,QAAA,gBACxCzf,OAAA;UAAKuJ,KAAK,EAAEgW,MAAM,CAACwG,oBAAqB;UAAAtG,QAAA,gBACtCzf,OAAA;YACEuJ,KAAK,EAAEgW,MAAM,CAACyG,UAAW;YACzB9D,SAAS,EAAEpgB,QAAQ,GAAG,oBAAoB,GAAG,EAAG;YAChD0e,OAAO,EAAEnD,aAAc;YACvB,cAAW,SAAS;YAAAoC,QAAA,eAEpBzf,OAAA;cAAKuJ,KAAK,EAAEgW,MAAM,CAAC0G,YAAa;cAAC/D,SAAS,EAAEpgB,QAAQ,GAAG,qBAAqB,GAAG;YAAG;cAAA8d,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eACT/f,OAAA;YAAMuJ,KAAK,EAAEgW,MAAM,CAAC2G,WAAY;YAAAzG,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eAGN/f,OAAA;UAAKuJ,KAAK,EAAEgW,MAAM,CAAC4G,kBAAmB;UAAA1G,QAAA,gBACpCzf,OAAA;YACEuJ,KAAK,EAAEgW,MAAM,CAAC6G,QAAS;YACvBlE,SAAS,EAAEpgB,QAAQ,GAAG,kBAAkB,GAAG,EAAG;YAC9C0e,OAAO,EAAE7E,gBAAiB;YAC1B,cAAW,cAAc;YAAA8D,QAAA,eAEzBzf,OAAA;cAAK+J,KAAK,EAAC,IAAI;cAACE,MAAM,EAAC,IAAI;cAACiZ,OAAO,EAAC,WAAW;cAACK,IAAI,EAAC,OAAO;cAAA9D,QAAA,eAC1Dzf,OAAA;gBAAMojB,CAAC,EAAC;cAA0R;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACT/f,OAAA;YAAMuJ,KAAK,EAAEgW,MAAM,CAAC2G,WAAY;YAAAzG,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA7e,UAAU,iBACTlB,OAAA;QACEuJ,KAAK,EAAEgW,MAAM,CAAC8G,QAAS;QACvB7F,OAAO,EAAEA,CAAA,KAAMnU,MAAM,CAACC,QAAQ,CAACga,MAAM,CAAC,CAAE;QACxC,cAAW,OAAO;QAAA7G,QAAA,eAElBzf,OAAA;UAAK+J,KAAK,EAAC,IAAI;UAACE,MAAM,EAAC,IAAI;UAACiZ,OAAO,EAAC,WAAW;UAACK,IAAI,EAAC,OAAO;UAAA9D,QAAA,eAC1Dzf,OAAA;YAAMojB,CAAC,EAAC;UAAyN;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL7e,UAAU,iBACTlB,OAAA;MACEuJ,KAAK,EAAEgW,MAAM,CAACgH,oBAAqB;MACnCrE,SAAS,EAAEpgB,QAAQ,GAAG,YAAY,GAAG,EAAG;MACxC0e,OAAO,EAAEA,CAAA,KAAMtZ,qBAAqB,CAAC,IAAI,CAAE;MAC3C,cAAW,mBAAmB;MAAAuY,QAAA,gBAE9Bzf,OAAA;QAAK+J,KAAK,EAAC,IAAI;QAACE,MAAM,EAAC,IAAI;QAACiZ,OAAO,EAAC,WAAW;QAACK,IAAI,EAAC,OAAO;QAAA9D,QAAA,eAC1Dzf,OAAA;UAAMojB,CAAC,EAAC;QAA64B;UAAAxD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACp5B,CAAC,eACN/f,OAAA;QAAMuJ,KAAK,EAAEgW,MAAM,CAACiH,aAAc;QAAA/G,QAAA,GAAE1Y,aAAa,EAAC,IAAE;MAAA;QAAA6Y,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CACT,EAGA9Y,kBAAkB,iBACjBjH,OAAA;MACEuJ,KAAK,EAAEgW,MAAM,CAACkH,YAAa;MAC3BjG,OAAO,EAAEA,CAAA,KAAMtZ,qBAAqB,CAAC,KAAK,CAAE;MAC5Cgb,SAAS,EAAC,eAAe;MAAAzC,QAAA,eAEzBzf,OAAA;QACEuJ,KAAK,EAAEgW,MAAM,CAACmH,cAAe;QAC7BlG,OAAO,EAAGlP,CAAC,IAAKA,CAAC,CAACqV,eAAe,CAAC,CAAE;QACpCzE,SAAS,EAAC,eAAe;QAAAzC,QAAA,gBAEzBzf,OAAA;UAAKuJ,KAAK,EAAEgW,MAAM,CAACqH,WAAY;UAAAnH,QAAA,gBAC7Bzf,OAAA;YAAIuJ,KAAK,EAAEgW,MAAM,CAACsH,UAAW;YAAApH,QAAA,EAAC;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpD/f,OAAA;YACEuJ,KAAK,EAAEgW,MAAM,CAACuH,aAAc;YAC5BtG,OAAO,EAAEA,CAAA,KAAMtZ,qBAAqB,CAAC,KAAK,CAAE;YAC5C,cAAW,OAAO;YAAAuY,QAAA,eAElBzf,OAAA;cAAK+J,KAAK,EAAC,IAAI;cAACE,MAAM,EAAC,IAAI;cAACiZ,OAAO,EAAC,WAAW;cAACK,IAAI,EAAC,cAAc;cAAA9D,QAAA,eACjEzf,OAAA;gBAAMojB,CAAC,EAAC;cAAuG;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN/f,OAAA;UAAKuJ,KAAK,EAAEgW,MAAM,CAACwH,YAAa;UAAAtH,QAAA,gBAE9Bzf,OAAA;YAAKuJ,KAAK,EAAEgW,MAAM,CAACyH,eAAgB;YAAAvH,QAAA,eACjCzf,OAAA;cACEuJ,KAAK,EAAE;gBACL,GAAGgW,MAAM,CAAC0H,YAAY;gBACtB,GAAG1H,MAAM,CAAC2H;cACZ,CAAE;cACF1G,OAAO,EAAEA,CAAA,KAAMhD,kBAAkB,CAAC,KAAK,CAAE;cAAAiC,QAAA,EAC1C;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN/f,OAAA;YAAKuJ,KAAK,EAAEgW,MAAM,CAAC4H,eAAgB;YAAA1H,QAAA,gBACjCzf,OAAA;cAAOuJ,KAAK,EAAEgW,MAAM,CAAC6H,WAAY;cAAA3H,QAAA,GAAC,cACpB,EAAC1Y,aAAa,EAAC,IAC7B;YAAA;cAAA6Y,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/f,OAAA;cACE2C,IAAI,EAAC,OAAO;cACZyC,GAAG,EAAE,EAAG;cACRC,GAAG,EAAE,EAAG;cACR6R,KAAK,EAAEnQ,aAAc;cACrBqb,QAAQ,EAAG9Q,CAAC,IAAKqM,qBAAqB,CAAC7Q,QAAQ,CAACwE,CAAC,CAACwN,MAAM,CAAC5H,KAAK,CAAC,CAAE;cACjE3N,KAAK,EAAEgW,MAAM,CAAC8H;YAAO;cAAAzH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACF/f,OAAA;cAAKuJ,KAAK,EAAEgW,MAAM,CAAC+H,YAAa;cAAA7H,QAAA,gBAC9Bzf,OAAA;gBAAAyf,QAAA,EAAM;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjB/f,OAAA;gBAAAyf,QAAA,EAAM;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAGN/f,OAAA;cAAKuJ,KAAK,EAAEgW,MAAM,CAACgI,aAAc;cAAA9H,QAAA,gBAC/Bzf,OAAA;gBACEuJ,KAAK,EAAEgW,MAAM,CAACiI,YAAa;gBAC3BhH,OAAO,EAAEA,CAAA,KAAM7C,qBAAqB,CAACrP,kBAAkB,CAAE;gBAAAmR,QAAA,GAC1D,WACU,EAACnR,kBAAkB,EAAC,KAC/B;cAAA;gBAAAsR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/f,OAAA;gBACEuJ,KAAK,EAAEgW,MAAM,CAACiI,YAAa;gBAC3BhH,OAAO,EAAEA,CAAA,KAAM7C,qBAAqB,CAAC,EAAE,CAAE;gBAAA8B,QAAA,EAC1C;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/f,OAAA;gBACEuJ,KAAK,EAAEgW,MAAM,CAACiI,YAAa;gBAC3BhH,OAAO,EAAEA,CAAA,KAAM7C,qBAAqB,CAAC,EAAE,CAAE;gBAAA8B,QAAA,EAC1C;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAve,oBAAoB,iBACnBxB,OAAA;MACE0hB,GAAG,EAAEzZ,QAAS;MACdsB,KAAK,EAAE;QACL,GAAGgW,MAAM,CAACkI,gBAAgB;QAC1BxD,SAAS,EAAE,cAActc,aAAa,KAAK;QAC3C+f,WAAW,EAAE;MACf,CAAE;MACFxF,SAAS,EAAEpgB,QAAQ,GAAG,sBAAsB,GAAG,EAAG;MAClD,cAAW,MAAM;MACjB6lB,IAAI,EAAC,QAAQ;MACbC,YAAY,EAAErJ,gBAAiB;MAC/BsJ,WAAW,EAAEpJ,eAAgB;MAC7BqJ,UAAU,EAAElJ,cAAe;MAAAa,QAAA,gBAE3Bzf,OAAA;QACEuJ,KAAK,EAAEgW,MAAM,CAACwI,UAAW;QACzB,eAAY,MAAM;QAClBH,YAAY,EAAErJ,gBAAiB;QAC/BsJ,WAAW,EAAEpJ,eAAgB;QAC7BqJ,UAAU,EAAElJ;MAAe;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,EAED,CAACzd,SAAS,CAACK,IAAI,iBACd3C,OAAA;QAAKuJ,KAAK,EAAEgW,MAAM,CAACyI,WAAY;QAAAvI,QAAA,gBAC7Bzf,OAAA;UACEuJ,KAAK,EAAE;YACL,GAAGgW,MAAM,CAAC0I,GAAG;YACb,IAAIvmB,SAAS,KAAK,SAAS,GAAG6d,MAAM,CAAC7d,SAAS,GAAG,CAAC,CAAC;UACrD,CAAE;UACF8e,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,SAAS,CAAE;UAAA6B,QAAA,EAC3C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/f,OAAA;UACEuJ,KAAK,EAAE;YACL,GAAGgW,MAAM,CAAC0I,GAAG;YACb,IAAIvmB,SAAS,KAAK,WAAW,GAAG6d,MAAM,CAAC7d,SAAS,GAAG,CAAC,CAAC;UACvD,CAAE;UACF8e,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,WAAW,CAAE;UAAA6B,QAAA,EAC7C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,eACD/f,OAAA;QAAKuJ,KAAK,EAAEgW,MAAM,CAAC2I,aAAc;QAAChG,SAAS,EAAC,gBAAgB;QAAAzC,QAAA,EACzDxB,kBAAkB,CAAC,CAAC,CAACzT,MAAM,GAAG,CAAC,GAC9ByT,kBAAkB,CAAC,CAAC,CAACkK,GAAG,CAAC,CAAChK,OAAO,EAAEiK,KAAK,KAAK;UAC3C;UACA,IAAI,CAACjK,OAAO,EAAE,OAAO,IAAI;UAEzB,MAAMkK,UAAU,GAAG,CAAC,OAAOjnB,eAAe,KAAK,QAAQ,GAAGA,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgH,IAAI,GAAGhH,eAAe,MAAM+c,OAAO,CAAC/V,IAAI;UAEnH,oBACEpI,OAAA;YAEEuJ,KAAK,EAAE;cACL,GAAGgW,MAAM,CAAC+I,WAAW;cACrBC,WAAW,EAAEF,UAAU,GAAG,SAAS,GAAG,SAAS;cAC/CrF,eAAe,EAAEqF,UAAU,GAAG,SAAS,GAAG;YAC5C,CAAE;YACF/M,KAAK,EAAE,GAAG6C,OAAO,CAAChW,IAAI,MAAMgW,OAAO,CAAC9V,YAAY,IAAI,KAAK,IAAK;YAC9DmY,OAAO,EAAEA,CAAA,KAAMtC,mBAAmB,CAACC,OAAO,CAAE;YAC5C,cAAY,UAAUA,OAAO,CAAChW,IAAI,IAAIgW,OAAO,CAAC9V,YAAY,IAAI,KAAK,IAAK;YAAAoX,QAAA,gBAExEzf,OAAA;cACEgJ,GAAG,EAAEmV,OAAO,CAAC/V,IAAK;cAClB8Y,GAAG,EAAE/C,OAAO,CAAChW,IAAK;cAClBoB,KAAK,EAAEgW,MAAM,CAACiJ,YAAa;cAC3BpH,OAAO,EAAG9P,CAAC,IAAK;gBACdA,CAAC,CAACwN,MAAM,CAAChH,aAAa,CAACvO,KAAK,CAACuC,OAAO,GAAG,MAAM;cAC/C;YAAE;cAAA8T,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF/f,OAAA;cAAKuJ,KAAK,EAAEgW,MAAM,CAACkJ,YAAa;cAAAhJ,QAAA,gBAC9Bzf,OAAA;gBAAKuJ,KAAK,EAAEgW,MAAM,CAACpL,WAAY;gBAAAsL,QAAA,EAAEtB,OAAO,CAAChW;cAAI;gBAAAyX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACnDre,SAAS,KAAK,SAAS,IAAIyc,OAAO,CAAC9V,YAAY,iBAC9CrI,OAAA;gBAAKuJ,KAAK,EAAEgW,MAAM,CAACmJ,WAAY;gBAAAjJ,QAAA,GAAEtB,OAAO,CAAC9V,YAAY,EAAC,IAAE;cAAA;gBAAAuX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAC9D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAvBDqI,KAAK;YAAAxI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBJ,CAAC;QAEb,CAAC,CAAC,gBAEF/f,OAAA;UAAKuJ,KAAK,EAAEgW,MAAM,CAACoJ,iBAAkB;UAAAlJ,QAAA,gBACnCzf,OAAA;YAAKuJ,KAAK,EAAEgW,MAAM,CAACqJ,cAAe;YAAAnJ,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3C/f,OAAA;YAAKuJ,KAAK,EAAEgW,MAAM,CAACsJ,eAAgB;YAAApJ,QAAA,EAAC;UAAqB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/D/f,OAAA;YAAKuJ,KAAK,EAAEgW,MAAM,CAACuJ,cAAe;YAAArJ,QAAA,EAAC;UAEnC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN/f,OAAA;YAAKuJ,KAAK,EAAEgW,MAAM,CAACwJ,iBAAkB;YAAAtJ,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAjf,EAAA,CAt3EMF,KAAK;AAAAooB,EAAA,GAALpoB,KAAK;AAu3EX,MAAM2e,MAAM,GAAG;EACbkB,SAAS,EAAE;IACTpO,QAAQ,EAAE,UAAU;IACpBpI,MAAM,EAAE,4BAA4B;IACpC6B,OAAO,EAAE,MAAM;IACfmd,aAAa,EAAE,QAAQ;IACvBjG,eAAe,EAAE,SAAS;IAC1BzB,KAAK,EAAE,MAAM;IACb2H,UAAU,EAAE,4EAA4E;IACxF7E,QAAQ,EAAE,QAAQ;IAClBqD,WAAW,EAAE,cAAc;IAC3ByB,uBAAuB,EAAE,aAAa;IACtCC,uBAAuB,EAAE,OAAO,CAAC;EACnC,CAAC;EACD3H,eAAe,EAAE;IACf4H,IAAI,EAAE,CAAC;IACPhX,QAAQ,EAAE,UAAU;IACpBgS,QAAQ,EAAE,QAAQ;IAClBrB,eAAe,EAAE,MAAM;IACvBlX,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACD5C,UAAU,EAAE;IACV5X,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdua,SAAS,EAAE,OAAO;IAClBP,SAAS,EAAE,WAAW,CAAC;EACzB,CAAC;EACDlC,aAAa,EAAE;IACb1P,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,CAAC;IACNe,IAAI,EAAE,CAAC;IACPhb,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdua,SAAS,EAAE,OAAO;IAClB1Y,OAAO,EAAE,MAAM;IACfwd,eAAe,EAAE,WAAW,CAAC;EAC/B,CAAC;EAED/I,OAAO,EAAE;IACPlO,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,MAAM;IACXe,IAAI,EAAE,MAAM;IACZ/B,eAAe,EAAE,oBAAoB;IACrCzB,KAAK,EAAE,OAAO;IACdyD,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,KAAK;IACnBpB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjByF,MAAM,EAAE,SAAS;IACjBlE,MAAM,EAAE,EAAE;IACVmE,MAAM,EAAE,MAAM;IACdpE,SAAS,EAAE,+BAA+B;IAC1CqE,UAAU,EAAE,eAAe;IAC3B3d,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBxa,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdyf,OAAO,EAAE;EACX,CAAC;EACDC,OAAO,EAAE;IACPtX,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,MAAM;IACXe,IAAI,EAAE,MAAM;IACZ/B,eAAe,EAAE,oBAAoB;IACrCzB,KAAK,EAAE,OAAO;IACdyD,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,KAAK;IACnBpB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjByF,MAAM,EAAE,SAAS;IACjBlE,MAAM,EAAE,EAAE;IACVmE,MAAM,EAAE,MAAM;IACdpE,SAAS,EAAE,+BAA+B;IAC1CqE,UAAU,EAAE,eAAe;IAC3B3d,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBxa,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdyf,OAAO,EAAE;EACX,CAAC;EACDE,eAAe,EAAE;IACfvX,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,MAAM;IACX6F,KAAK,EAAE,MAAM;IACbxE,MAAM,EAAE,EAAE;IACVvZ,OAAO,EAAE,MAAM;IACfmd,aAAa,EAAE,QAAQ;IACvB3E,UAAU,EAAE,QAAQ;IACpBwF,GAAG,EAAE,KAAK;IACV9E,OAAO,EAAE,MAAM;IACfhC,eAAe,EAAE,oBAAoB;IACrCiC,YAAY,EAAE,MAAM;IACpB8E,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClC5E,SAAS,EAAE,+BAA+B;IAC1CoE,MAAM,EAAE;EACV,CAAC;EACDS,WAAW,EAAE;IACX5X,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,MAAM;IACXe,IAAI,EAAE,MAAM;IACZhb,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACd+Y,eAAe,EAAE,oBAAoB;IACrCiC,YAAY,EAAE,MAAM;IACpBuE,MAAM,EAAE,oCAAoC;IAC5CnE,MAAM,EAAE,CAAC;IACToE,UAAU,EAAE;EACd,CAAC;EACDS,YAAY,EAAE;IACZ7X,QAAQ,EAAE,UAAU;IACpBtI,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdgb,YAAY,EAAE,KAAK;IACnBuE,MAAM,EAAE,MAAM;IACdD,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3B3d,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBc,MAAM,EAAE,EAAE;IACV8E,MAAM,EAAE,KAAK;IACb/E,SAAS,EAAE,8BAA8B;IACzCsE,OAAO,EAAE,MAAM;IACf,SAAS,EAAE;MACTzF,SAAS,EAAE;IACb;EACF,CAAC;EACDmG,WAAW,EAAE;IACXvG,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,OAAO;IACd8I,UAAU,EAAE,8BAA8B;IAC1CC,SAAS,EAAE,KAAK;IAChBtF,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,MAAM;IACpBjC,eAAe,EAAE,oBAAoB;IACrCuH,aAAa,EAAE;EACjB,CAAC;EACDjI,gBAAgB,EAAE;IAChBjQ,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,KAAK;IACVe,IAAI,EAAE,KAAK;IACXd,SAAS,EAAE,uBAAuB;IAClCoB,MAAM,EAAE,EAAE;IACVmF,SAAS,EAAE,QAAQ;IACnBrF,aAAa,EAAE,MAAM;IACrBH,OAAO,EAAE,WAAW;IACpBhC,eAAe,EAAE,oBAAoB;IACrCiC,YAAY,EAAE,MAAM;IACpB8E,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClC5E,SAAS,EAAE;EACb,CAAC;EACD7C,eAAe,EAAE;IACfsB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,SAAS;IAChB8I,UAAU,EAAE,8BAA8B;IAC1CI,YAAY,EAAE,KAAK;IACnBC,SAAS,EAAE;EACb,CAAC;EACDlI,aAAa,EAAE;IACbqB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,OAAO;IACd8I,UAAU,EAAE;EACd,CAAC;EACDvH,aAAa,EAAE;IACbzQ,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,KAAK;IACVe,IAAI,EAAE,KAAK;IACXd,SAAS,EAAE,uBAAuB;IAClCoB,MAAM,EAAE,EAAE;IACVmF,SAAS,EAAE,QAAQ;IACnBrF,aAAa,EAAE,MAAM;IACrBH,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpBjC,eAAe,EAAE,oBAAoB;IACrC+G,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClC5E,SAAS,EAAE;EACb,CAAC;EACDrC,UAAU,EAAE;IACVc,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,OAAO;IACd8I,UAAU,EAAE,8BAA8B;IAC1CrH,eAAe,EAAE,0BAA0B;IAC3CgC,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpBwF,YAAY,EAAE,KAAK;IACnBhB,UAAU,EAAE;EACd,CAAC;EACDkB,aAAa,EAAE;IACb9G,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,OAAO;IACd8I,UAAU,EAAE,8BAA8B;IAC1CrH,eAAe,EAAE,oBAAoB;IACrCgC,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE;EAChB,CAAC;EACDhC,SAAS,EAAE;IACT5Q,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,KAAK;IACVe,IAAI,EAAE,KAAK;IACXd,SAAS,EAAE,uBAAuB;IAClCla,KAAK,EAAE,KAAK;IACZ6gB,QAAQ,EAAE,OAAO;IACjB3gB,MAAM,EAAE,MAAM;IACdP,OAAO,EAAE,GAAG;IACZyb,aAAa,EAAE,MAAM;IACrBE,MAAM,EAAE,CAAC;IACT7b,MAAM,EAAE,iDAAiD;IACzDqhB,YAAY,EAAE,iDAAiD;IAC/DpB,UAAU,EAAE;EACd,CAAC;EACH1F,eAAe,EAAE;IACf1R,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,KAAK;IACVe,IAAI,EAAE,KAAK;IACXd,SAAS,EAAE,uBAAuB;IAClCoB,MAAM,EAAE,CAAC;IACTvZ,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBxa,KAAK,EAAE,MAAM;IAAE;IACf+gB,WAAW,EAAE,SAAS;IAAE;IACxBtO,QAAQ,EAAE,OAAO;IACjBuO,SAAS,EAAE,OAAO;IAAE;IACpB5F,aAAa,EAAE;EACjB,CAAC;EAECW,sBAAsB,EAAE;IACtBzT,QAAQ,EAAE,UAAU;IACpByS,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXd,SAAS,EAAE,kBAAkB;IAC7BnY,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,UAAU;IACtBwF,GAAG,EAAE,MAAM;IACXzE,MAAM,EAAE;EACV,CAAC;EACDU,oBAAoB,EAAE;IACpBja,OAAO,EAAE,MAAM;IACfmd,aAAa,EAAE,QAAQ;IACvB3E,UAAU,EAAE,QAAQ;IACpBwF,GAAG,EAAE;EACP,CAAC;EACD3D,kBAAkB,EAAE;IAClBra,OAAO,EAAE,MAAM;IACfmd,aAAa,EAAE,QAAQ;IACvB3E,UAAU,EAAE,QAAQ;IACpBwF,GAAG,EAAE;EACP,CAAC;EACD5D,WAAW,EAAE;IACXrC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,OAAO;IACd8I,UAAU,EAAE,8BAA8B;IAC1CrH,eAAe,EAAE,oBAAoB;IACrCgC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,MAAM;IACpBC,UAAU,EAAE,QAAQ;IACpB6E,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE;EACxB,CAAC;EACDhE,UAAU,EAAE;IACVjc,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACd+Y,eAAe,EAAE,0BAA0B;IAC3CiC,YAAY,EAAE,KAAK;IACnBnZ,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBgF,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,oCAAoC;IAC5CpE,SAAS,EAAE,+BAA+B;IAC1CsE,OAAO,EAAE,MAAM;IACf1E,OAAO,EAAE,CAAC;IACVmE,uBAAuB,EAAE,aAAa;IACtCzB,WAAW,EAAE;EACf,CAAC;EACDzB,YAAY,EAAE;IACZlc,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACd+Y,eAAe,EAAE,SAAS;IAC1BiC,YAAY,EAAE,KAAK;IACnBwE,UAAU,EAAE;EACd,CAAC;EACDrD,QAAQ,EAAE;IACRrc,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACd+Y,eAAe,EAAE,yBAAyB;IAC1CiC,YAAY,EAAE,KAAK;IACnBnZ,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBgF,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,oCAAoC;IAC5CpE,SAAS,EAAE,+BAA+B;IAC1CsE,OAAO,EAAE,MAAM;IACf1E,OAAO,EAAE,CAAC;IACVmE,uBAAuB,EAAE,aAAa;IACtCzB,WAAW,EAAE;EACf,CAAC;EACDrB,QAAQ,EAAE;IACRhU,QAAQ,EAAE,UAAU;IACpByS,MAAM,EAAE,MAAM;IACd+E,KAAK,EAAE,MAAM;IACb9f,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACd+Y,eAAe,EAAE,oBAAoB;IACrCiC,YAAY,EAAE,KAAK;IACnBnZ,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBgF,MAAM,EAAE,SAAS;IACjBlE,MAAM,EAAE,EAAE;IACVoE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,MAAM;IACdpE,SAAS,EAAE,+BAA+B;IAC1CsE,OAAO,EAAE,MAAM;IACf1E,OAAO,EAAE;EACX,CAAC;EAED;EACAuB,oBAAoB,EAAE;IACpBlU,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,MAAM;IACX6F,KAAK,EAAE,MAAM;IACb7G,eAAe,EAAE,SAAS;IAC1BzB,KAAK,EAAE,OAAO;IACdyD,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpBpB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjByF,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,MAAM;IACd1d,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBwF,GAAG,EAAE,KAAK;IACVJ,OAAO,EAAE,MAAM;IACfD,UAAU,EAAE,eAAe;IAC3BpE,MAAM,EAAE,EAAE;IACVD,SAAS,EAAE,mCAAmC;IAC9C2F,SAAS,EAAE,MAAM;IACjBvO,QAAQ,EAAE,MAAM;IAChB2M,uBAAuB,EAAE,aAAa;IACtCzB,WAAW,EAAE;EACf,CAAC;EACDlB,aAAa,EAAE;IACb3C,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE;EACd,CAAC;EAED;EACA2C,YAAY,EAAE;IACZpU,QAAQ,EAAE,OAAO;IACjB2R,GAAG,EAAE,CAAC;IACNe,IAAI,EAAE,CAAC;IACP8E,KAAK,EAAE,CAAC;IACR/E,MAAM,EAAE,CAAC;IACT9B,eAAe,EAAE,oBAAoB;IACrClX,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBc,MAAM,EAAE,EAAE;IACVL,OAAO,EAAE,MAAM;IACf+E,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE,WAAW;IACjCtC,WAAW,EAAE;EACf,CAAC;EACDhB,cAAc,EAAE;IACd1D,eAAe,EAAE,OAAO;IACxBiC,YAAY,EAAE,MAAM;IACpBlb,KAAK,EAAE,MAAM;IACb6gB,QAAQ,EAAE,MAAM;IAChBI,SAAS,EAAE,MAAM;IACjB3G,QAAQ,EAAE,QAAQ;IAClBe,SAAS,EAAE,gCAAgC;IAC3CtZ,OAAO,EAAE,MAAM;IACfmd,aAAa,EAAE,QAAQ;IACvBkB,MAAM,EAAE,MAAM;IACd9X,QAAQ,EAAE;EACZ,CAAC;EACDuU,WAAW,EAAE;IACX9a,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,eAAe;IAC/BS,OAAO,EAAE,qBAAqB;IAC9BiG,YAAY,EAAE;EAChB,CAAC;EACDpE,UAAU,EAAE;IACVhD,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,SAAS;IAChB4I,MAAM,EAAE;EACV,CAAC;EACDrD,aAAa,EAAE;IACb/c,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdgb,YAAY,EAAE,KAAK;IACnBuE,MAAM,EAAE,MAAM;IACdxG,eAAe,EAAE,SAAS;IAC1BzB,KAAK,EAAE,MAAM;IACbgI,MAAM,EAAE,SAAS;IACjBzd,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBkF,UAAU,EAAE,eAAe;IAC3BC,OAAO,EAAE;EACX,CAAC;EACD3C,YAAY,EAAE;IACZ/B,OAAO,EAAE,qBAAqB;IAC9BlZ,OAAO,EAAE,MAAM;IACfmd,aAAa,EAAE,QAAQ;IACvBa,GAAG,EAAE,MAAM;IACXoB,SAAS,EAAE,MAAM;IACjB9B,uBAAuB,EAAE,OAAO;IAChC4B,SAAS,EAAE;EACb,CAAC;EAED;EACAG,gBAAgB,EAAE;IAChBrf,OAAO,EAAE,MAAM;IACfmd,aAAa,EAAE,QAAQ;IACvB3E,UAAU,EAAE,QAAQ;IACpBwF,GAAG,EAAE;EACP,CAAC;EACDsB,cAAc,EAAE;IACdvH,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,SAAS;IAChB4I,MAAM,EAAE,CAAC;IACTK,SAAS,EAAE;EACb,CAAC;EACDa,iBAAiB,EAAE;IACjBxH,QAAQ,EAAE,MAAM;IAChBtC,KAAK,EAAE,MAAM;IACb4I,MAAM,EAAE,CAAC;IACTK,SAAS,EAAE,QAAQ;IACnBc,UAAU,EAAE;EACd,CAAC;EACDtE,eAAe,EAAE;IACflb,OAAO,EAAE,MAAM;IACfge,GAAG,EAAE,MAAM;IACX/f,KAAK,EAAE,MAAM;IACb6gB,QAAQ,EAAE;EACZ,CAAC;EACD3D,YAAY,EAAE;IACZoC,IAAI,EAAE,CAAC;IACPrE,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,MAAM;IACpBpB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjByF,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,mBAAmB;IAC3BxG,eAAe,EAAE,SAAS;IAC1BzB,KAAK,EAAE,MAAM;IACbmI,OAAO,EAAE;EACX,CAAC;EACDxC,kBAAkB,EAAE;IAClBlE,eAAe,EAAE,SAAS;IAC1BzB,KAAK,EAAE,SAAS;IAChBgH,WAAW,EAAE,SAAS;IACtBnD,SAAS,EAAE;EACb,CAAC;EACD+B,eAAe,EAAE;IACfpd,KAAK,EAAE,MAAM;IACb6gB,QAAQ,EAAE,OAAO;IACjB9e,OAAO,EAAE,MAAM;IACfmd,aAAa,EAAE,QAAQ;IACvBa,GAAG,EAAE;EACP,CAAC;EACD1C,WAAW,EAAE;IACXvD,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,MAAM;IACbiJ,SAAS,EAAE,QAAQ;IACnB1e,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBuF,GAAG,EAAE;EACP,CAAC;EACDyB,UAAU,EAAE;IACV1H,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,SAAS;IAChByB,eAAe,EAAE,yBAAyB;IAC1CgC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE;EAChB,CAAC;EACDoC,MAAM,EAAE;IACNtd,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,KAAK;IACbgb,YAAY,EAAE,KAAK;IACnBuG,UAAU,EAAE,SAAS;IACrB9B,OAAO,EAAE,MAAM;IACfH,MAAM,EAAE,SAAS;IACjBkC,gBAAgB,EAAE,MAAM;IACxBC,UAAU,EAAE;EACd,CAAC;EACDpE,YAAY,EAAE;IACZxb,OAAO,EAAE,MAAM;IACfyY,cAAc,EAAE,eAAe;IAC/BV,QAAQ,EAAE,MAAM;IAChBtC,KAAK,EAAE,MAAM;IACb+I,SAAS,EAAE;EACb,CAAC;EACD/C,aAAa,EAAE;IACbzb,OAAO,EAAE,MAAM;IACfge,GAAG,EAAE,KAAK;IACV/f,KAAK,EAAE,MAAM;IACb6gB,QAAQ,EAAE;EACZ,CAAC;EACDpD,YAAY,EAAE;IACZ6B,IAAI,EAAE,CAAC;IACPrE,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,KAAK;IACnBpB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjByF,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,mBAAmB;IAC3BxG,eAAe,EAAE,SAAS;IAC1BzB,KAAK,EAAE,MAAM;IACbmI,OAAO,EAAE;EACX,CAAC;EACDiC,cAAc,EAAE;IACd5hB,KAAK,EAAE,MAAM;IACb6gB,QAAQ,EAAE,OAAO;IACjB5F,OAAO,EAAE,WAAW;IACpBhC,eAAe,EAAE,SAAS;IAC1BzB,KAAK,EAAE,SAAS;IAChB0D,YAAY,EAAE,MAAM;IACpBpB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjByF,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,MAAM;IACdE,OAAO,EAAE,MAAM;IACftE,SAAS,EAAE;EACb,CAAC;EAED;EACAwG,eAAe,EAAE;IACfJ,UAAU,EAAE,mDAAmD;IAC/DvG,YAAY,EAAE,MAAM;IACpBlb,KAAK,EAAE,MAAM;IACb6gB,QAAQ,EAAE,OAAO;IACjBvG,QAAQ,EAAE,QAAQ;IAClBe,SAAS,EAAE,qEAAqE;IAChFtZ,OAAO,EAAE,MAAM;IACfmd,aAAa,EAAE,QAAQ;IACvBkB,MAAM,EAAE,MAAM;IACd9X,QAAQ,EAAE,UAAU;IACpBmX,MAAM,EAAE;EACV,CAAC;EACDqC,gBAAgB,EAAE;IAChBrB,SAAS,EAAE,QAAQ;IACnBxF,OAAO,EAAE,qBAAqB;IAC9BwG,UAAU,EAAE,mDAAmD;IAC/DjK,KAAK,EAAE,OAAO;IACdlP,QAAQ,EAAE;EACZ,CAAC;EACDyZ,eAAe,EAAE;IACfjI,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB2G,YAAY,EAAE,KAAK;IACnBJ,UAAU,EAAE;EACd,CAAC;EACD0B,kBAAkB,EAAE;IAClBlI,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBpa,OAAO,EAAE,GAAG;IACZ6gB,aAAa,EAAE;EACjB,CAAC;EACDyB,iBAAiB,EAAE;IACjBhH,OAAO,EAAE,MAAM;IACflZ,OAAO,EAAE,MAAM;IACfmd,aAAa,EAAE,QAAQ;IACvBa,GAAG,EAAE;EACP,CAAC;EACDmC,eAAe,EAAE;IACfngB,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,YAAY;IACxBwF,GAAG,EAAE;EACP,CAAC;EACDoC,gBAAgB,EAAE;IAChBniB,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdgb,YAAY,EAAE,KAAK;IACnBuG,UAAU,EAAE,mDAAmD;IAC/DjK,KAAK,EAAE,OAAO;IACdzV,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBV,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBqI,UAAU,EAAE,CAAC;IACb/G,SAAS,EAAE;EACb,CAAC;EACDgH,cAAc,EAAE;IACdvI,QAAQ,EAAE,MAAM;IAChByH,UAAU,EAAE,KAAK;IACjB/J,KAAK,EAAE,MAAM;IACb8H,IAAI,EAAE,CAAC;IACPgD,UAAU,EAAE;EACd,CAAC;EACDC,aAAa,EAAE;IACbja,QAAQ,EAAE,UAAU;IACpBtI,KAAK,EAAE,MAAM;IACbib,OAAO,EAAE,WAAW;IACpBwG,UAAU,EAAE,mDAAmD;IAC/DjK,KAAK,EAAE,SAAS;IAChB0D,YAAY,EAAE,eAAe;IAC7BpB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjByF,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,MAAM;IACdE,OAAO,EAAE,MAAM;IACfrF,QAAQ,EAAE;EACZ,CAAC;EACDkI,eAAe,EAAE;IACfla,QAAQ,EAAE,UAAU;IACpBgT,MAAM,EAAE;EACV,CAAC;EACDmH,eAAe,EAAE;IACfna,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,CAAC;IACNe,IAAI,EAAE,CAAC;IACP8E,KAAK,EAAE,CAAC;IACR/E,MAAM,EAAE,CAAC;IACT0G,UAAU,EAAE,wEAAwE;IACpF9hB,OAAO,EAAE,CAAC;IACV+f,UAAU,EAAE;EACd,CAAC;EAED;EACAhH,oBAAoB,EAAE;IACpBpQ,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,KAAK;IAAE;IACZe,IAAI,EAAE,KAAK;IACXd,SAAS,EAAE,uBAAuB;IAClCoB,MAAM,EAAE,EAAE;IACVF,aAAa,EAAE;EACjB,CAAC;EACDzC,eAAe,EAAE;IACfmB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,OAAO;IACdiJ,SAAS,EAAE,QAAQ;IACnBxF,OAAO,EAAE,UAAU;IACnBhC,eAAe,EAAE,oBAAoB;IACrCiC,YAAY,EAAE,MAAM;IACpB8E,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE,WAAW;IACjC5E,SAAS,EAAE,8BAA8B;IACzCoE,MAAM,EAAE,oCAAoC;IAC5CA,MAAM,EAAE,oCAAoC;IAC5Ca,UAAU,EAAE;EACd,CAAC;EAID5C,gBAAgB,EAAE;IAChBpV,QAAQ,EAAE,UAAU;IACpByS,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACP8E,KAAK,EAAE,CAAC;IACR7G,eAAe,EAAE,2BAA2B;IAC5C+G,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCyC,mBAAmB,EAAE,MAAM;IAC3BC,oBAAoB,EAAE,MAAM;IAC5B1H,OAAO,EAAE,MAAM;IACfgG,SAAS,EAAE,MAAM;IACjBlf,OAAO,EAAE,MAAM;IACfmd,aAAa,EAAE,QAAQ;IACvB5D,MAAM,EAAE,EAAE;IACVD,SAAS,EAAE,iCAAiC;IAC5CoE,MAAM,EAAE,MAAM;IACdvF,SAAS,EAAE,eAAe;IAC1BwF,UAAU,EAAE,yBAAyB;IACrCpF,QAAQ,EAAE,QAAQ;IAClBqD,WAAW,EAAE,MAAM;IACnBiF,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,MAAM;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EACDC,QAAQ,EAAE;IACRza,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,MAAM;IACX6F,KAAK,EAAE,MAAM;IACbtI,KAAK,EAAE,MAAM;IACbgI,MAAM,EAAE,SAAS;IACjBlE,MAAM,EAAE,EAAE;IACVtb,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACd6B,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBU,YAAY,EAAE,KAAK;IACnBjC,eAAe,EAAE,oBAAoB;IACrCyG,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,MAAM;IACdE,OAAO,EAAE,MAAM;IACf1E,OAAO,EAAE;EACX,CAAC;EACDgD,WAAW,EAAE;IACXlc,OAAO,EAAE,MAAM;IACf2e,YAAY,EAAE,MAAM;IACpBzH,eAAe,EAAE,SAAS;IAC1BiC,YAAY,EAAE,MAAM;IACpBD,OAAO,EAAE,KAAK;IACd8E,GAAG,EAAE;EACP,CAAC;EACD7B,GAAG,EAAE;IACHoB,IAAI,EAAE,CAAC;IACPmB,SAAS,EAAE,QAAQ;IACnBxF,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,KAAK;IACnBpB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjByF,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BlI,KAAK,EAAE,MAAM;IACbmI,OAAO,EAAE,MAAM;IACfF,MAAM,EAAE,MAAM;IACdxG,eAAe,EAAE,aAAa;IAC9BmG,uBAAuB,EAAE,aAAa;IACtCzB,WAAW,EAAE;EACf,CAAC;EACDhmB,SAAS,EAAE;IACTshB,eAAe,EAAE,SAAS;IAC1BzB,KAAK,EAAE,SAAS;IAChB6D,SAAS,EAAE;EACb,CAAC;EACD8C,aAAa,EAAE;IACbpc,OAAO,EAAE,MAAM;IACfihB,mBAAmB,EAAE,uCAAuC;IAC5DjD,GAAG,EAAE,MAAM;IACXkB,SAAS,EAAE,oBAAoB;IAC/BE,SAAS,EAAE,MAAM;IACjB8B,aAAa,EAAE,MAAM;IACrBC,cAAc,EAAE,MAAM;IACtBC,cAAc,EAAE,cAAc;IAC9B9D,uBAAuB,EAAE;EAC3B,CAAC;EACDd,WAAW,EAAE;IACXjW,QAAQ,EAAE,UAAU;IACpBtI,KAAK,EAAE,MAAM;IACb+gB,WAAW,EAAE,KAAK;IAClB9H,eAAe,EAAE,SAAS;IAC1BiC,YAAY,EAAE,MAAM;IACpBnZ,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBgF,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BD,MAAM,EAAE,mBAAmB;IAC3BnF,QAAQ,EAAE,QAAQ;IAClBe,SAAS,EAAE,+BAA+B;IAC1CJ,OAAO,EAAE,KAAK;IACd0E,OAAO,EAAE,MAAM;IACfP,uBAAuB,EAAE,aAAa;IACtCzB,WAAW,EAAE;EACf,CAAC;EACDc,YAAY,EAAE;IACZze,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdua,SAAS,EAAE,SAAS;IACpBS,YAAY,EAAE,KAAK;IACnBjC,eAAe,EAAE;EACnB,CAAC;EACDyF,YAAY,EAAE;IACZpW,QAAQ,EAAE,UAAU;IACpByS,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,KAAK;IACX8E,KAAK,EAAE,KAAK;IACZhG,QAAQ,EAAE,KAAK;IACftC,KAAK,EAAE,MAAM;IACbiJ,SAAS,EAAE,QAAQ;IACnBxH,eAAe,EAAE,2BAA2B;IAC5CiC,YAAY,EAAE,KAAK;IACnBD,OAAO,EAAE,SAAS;IAClBX,QAAQ,EAAE;EACZ,CAAC;EACDlQ,WAAW,EAAE;IACX0P,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,KAAK;IACjBoB,UAAU,EAAE,QAAQ;IACpBiI,YAAY,EAAE,UAAU;IACxB9I,QAAQ,EAAE,QAAQ;IAClBoG,YAAY,EAAE;EAChB,CAAC;EACD/B,WAAW,EAAE;IACX7E,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,SAAS;IAChB2D,UAAU,EAAE;EACd,CAAC;EACD6C,UAAU,EAAE;IACVhe,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,KAAK;IACb+Y,eAAe,EAAE,SAAS;IAC1BiC,YAAY,EAAE,KAAK;IACnBkF,MAAM,EAAE,aAAa;IACrBZ,MAAM,EAAE,MAAM;IACd7B,WAAW,EAAE,MAAM;IACnBkF,UAAU,EAAE,MAAM;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EAEDrN,gBAAgB,EAAE;IAChBnN,QAAQ,EAAE,UAAU;IACpBpI,MAAM,EAAE,OAAO;IACf6B,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBvB,eAAe,EAAE,SAAS;IAC1BgC,OAAO,EAAE;EACX,CAAC;EACDtF,WAAW,EAAE;IACXsD,eAAe,EAAE,OAAO;IACxBgC,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,MAAM;IACpBG,SAAS,EAAE,gCAAgC;IAC3CoF,SAAS,EAAE,QAAQ;IACnBI,QAAQ,EAAE,OAAO;IACjB7gB,KAAK,EAAE;EACT,CAAC;EACD4V,OAAO,EAAE;IACPkE,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,SAAS;IAChBkJ,YAAY,EAAE;EAChB,CAAC;EACDzK,UAAU,EAAE;IACV6D,QAAQ,EAAE,MAAM;IAChBtC,KAAK,EAAE,MAAM;IACbkJ,YAAY,EAAE,MAAM;IACpBa,UAAU,EAAE;EACd,CAAC;EACDrL,SAAS,EAAE;IACT+C,eAAe,EAAE,OAAO;IACxBgC,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE,MAAM;IACpBnZ,OAAO,EAAE,cAAc;IACvB2e,YAAY,EAAE,MAAM;IACpBrF,SAAS,EAAE;EACb,CAAC;EACD/E,MAAM,EAAE;IACNwD,QAAQ,EAAE,MAAM;IAChBtC,KAAK,EAAE,SAAS;IAChBkJ,YAAY,EAAE,MAAM;IACpB2C,SAAS,EAAE;EACb,CAAC;EACD9M,UAAU,EAAE;IACVuD,QAAQ,EAAE,MAAM;IAChBtC,KAAK,EAAE,MAAM;IACbkJ,YAAY,EAAE,MAAM;IACpB4C,SAAS,EAAE;EACb,CAAC;EACD1E,iBAAiB,EAAE;IACjB7c,OAAO,EAAE,MAAM;IACfmd,aAAa,EAAE,QAAQ;IACvB3E,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBS,OAAO,EAAE,WAAW;IACpBwF,SAAS,EAAE,QAAQ;IACnBvgB,MAAM,EAAE;EACV,CAAC;EACD2e,cAAc,EAAE;IACd/E,QAAQ,EAAE,MAAM;IAChB4G,YAAY,EAAE;EAChB,CAAC;EACD5B,eAAe,EAAE;IACfhF,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,MAAM;IACbkJ,YAAY,EAAE;EAChB,CAAC;EACD3B,cAAc,EAAE;IACdjF,QAAQ,EAAE,MAAM;IAChBtC,KAAK,EAAE,MAAM;IACb+J,UAAU,EAAE,KAAK;IACjBb,YAAY,EAAE,KAAK;IACnBG,QAAQ,EAAE;EACZ,CAAC;EACD7B,iBAAiB,EAAE;IACjBlF,QAAQ,EAAE,MAAM;IAChBtC,KAAK,EAAE,MAAM;IACb+J,UAAU,EAAE,KAAK;IACjBV,QAAQ,EAAE;EACZ,CAAC;EAED;EACAlK,iBAAiB,EAAE;IACjBrO,QAAQ,EAAE,OAAO;IACjB2R,GAAG,EAAE,CAAC;IACNe,IAAI,EAAE,CAAC;IACP8E,KAAK,EAAE,CAAC;IACR/E,MAAM,EAAE,CAAC;IACT9B,eAAe,EAAE,oBAAoB;IACrClX,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBc,MAAM,EAAE,IAAI;IACZ0E,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE;EACxB,CAAC;EACDrJ,eAAe,EAAE;IACfqC,eAAe,EAAE,OAAO;IACxBiC,YAAY,EAAE,MAAM;IACpBD,OAAO,EAAE,GAAG;IACZ4F,QAAQ,EAAE,OAAO;IACjB7gB,KAAK,EAAE,KAAK;IACZqb,SAAS,EAAE,gCAAgC;IAC3CoE,MAAM,EAAE;EACV,CAAC;EACD5I,iBAAiB,EAAE;IACjBoE,OAAO,EAAE,WAAW;IACpBwF,SAAS,EAAE;EACb,CAAC;EACD3J,eAAe,EAAE;IACfgD,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,SAAS;IAChBkJ,YAAY,EAAE,MAAM;IACpBN,MAAM,EAAE;EACV,CAAC;EACDrJ,gBAAgB,EAAE;IAChB2J,YAAY,EAAE;EAChB,CAAC;EACD1J,eAAe,EAAE;IACf8C,QAAQ,EAAE,MAAM;IAChBtC,KAAK,EAAE,MAAM;IACb+J,UAAU,EAAE,KAAK;IACjBb,YAAY,EAAE,MAAM;IACpBN,MAAM,EAAE,YAAY;IACpBK,SAAS,EAAE;EACb,CAAC;EACDxJ,gBAAgB,EAAE;IAChBgC,eAAe,EAAE,SAAS;IAC1BzB,KAAK,EAAE,OAAO;IACdiI,MAAM,EAAE,MAAM;IACdvE,YAAY,EAAE,MAAM;IACpBD,OAAO,EAAE,WAAW;IACpBnB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjByF,MAAM,EAAE,SAAS;IACjBE,UAAU,EAAE,eAAe;IAC3BrE,SAAS,EAAE,oCAAoC;IAC/C,QAAQ,EAAE;MACRpC,eAAe,EAAE,SAAS;MAC1BiB,SAAS,EAAE,kBAAkB;MAC7BmB,SAAS,EAAE;IACb;EACF,CAAC;EAED;EACApD,iBAAiB,EAAE;IACjB3P,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,MAAM;IACX6F,KAAK,EAAE,MAAM;IACb/d,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBwF,GAAG,EAAE,KAAK;IACVzE,MAAM,EAAE,EAAE;IACVL,OAAO,EAAE,UAAU;IACnBhC,eAAe,EAAE,oBAAoB;IACrCiC,YAAY,EAAE,MAAM;IACpB8E,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE,WAAW;IACjC5E,SAAS,EAAE,8BAA8B;IACzCoE,MAAM,EAAE;EACV,CAAC;EACDvH,gBAAgB,EAAE;IAChB4B,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,OAAO;IACd8I,UAAU,EAAE;EACd,CAAC;EAED;EACAiD,iBAAiB,EAAE;IACjBjb,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,MAAM;IACXe,IAAI,EAAE,MAAM;IACZjZ,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBwF,GAAG,EAAE,KAAK;IACVzE,MAAM,EAAE,EAAE;IACVrC,eAAe,EAAE,oBAAoB;IACrCgC,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,MAAM;IACpB8E,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE,WAAW;IACjC5E,SAAS,EAAE,8BAA8B;IACzCoE,MAAM,EAAE;EACV,CAAC;EACD+D,YAAY,EAAE;IACZxjB,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdua,SAAS,EAAE,SAAS;IACpBgJ,WAAW,EAAE,KAAK;IAClB1hB,OAAO,EAAE,cAAc;IACvB2hB,aAAa,EAAE;EACjB,CAAC;EACDC,YAAY,EAAE;IACZ7J,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,OAAO;IACd8I,UAAU,EAAE;EACd,CAAC;EAED;EACA1H,kBAAkB,EAAE;IAClBtQ,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,KAAK;IAAE;IACZe,IAAI,EAAE,KAAK;IACXd,SAAS,EAAE,uBAAuB;IAClCoB,MAAM,EAAE,EAAE;IACVmF,SAAS,EAAE,QAAQ;IACnBrF,aAAa,EAAE,MAAM;IACrBH,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,MAAM;IACpBjC,eAAe,EAAE,oBAAoB;IACrC+G,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE,WAAW;IACjC5E,SAAS,EAAE;EACb,CAAC;EACDxC,eAAe,EAAE;IACfiB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,OAAO;IACd8I,UAAU,EAAE,8BAA8B;IAC1CrH,eAAe,EAAE,0BAA0B;IAC3CgC,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,MAAM;IACpBwF,YAAY,EAAE,KAAK;IACnBhB,UAAU,EAAE;EACd,CAAC;EACD5G,kBAAkB,EAAE;IAClBgB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBvC,KAAK,EAAE,OAAO;IACd8I,UAAU,EAAE,8BAA8B;IAC1CrH,eAAe,EAAE,oBAAoB;IACrCgC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE;EAChB,CAAC;EAED;EACAhE,0BAA0B,EAAE;IAC1B5O,QAAQ,EAAE,UAAU;IACpB2R,GAAG,EAAE,MAAM;IACXe,IAAI,EAAE,MAAM;IACZjZ,OAAO,EAAE,MAAM;IACfwY,UAAU,EAAE,QAAQ;IACpBwF,GAAG,EAAE,MAAM;IACXzE,MAAM,EAAE,EAAE;IACVrC,eAAe,EAAE,iBAAiB;IAAE;IACpCgC,OAAO,EAAE,GAAG;IACZC,YAAY,EAAE,GAAG;IACjBG,SAAS,EAAE;EACb,CAAC;EACDjE,qBAAqB,EAAE;IACrBpX,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdua,SAAS,EAAE,SAAS;IACpB1Y,OAAO,EAAE,cAAc;IACvB2hB,aAAa,EAAE;EACjB,CAAC;EACDpM,mBAAmB,EAAE;IACnBvV,OAAO,EAAE,MAAM;IACfmd,aAAa,EAAE,QAAQ;IACvB3E,UAAU,EAAE,YAAY;IACxBgH,UAAU,EAAE;EACd,CAAC;EACDhK,aAAa,EAAE;IACbuC,QAAQ,EAAE,MAAM;IAChBtC,KAAK,EAAE,OAAO;IACduC,UAAU,EAAE,GAAG;IACfpa,OAAO,EAAE,IAAI;IACb6gB,aAAa,EAAE,QAAQ;IACvBE,YAAY,EAAE;EAChB,CAAC;EACDjJ,YAAY,EAAE;IACZqC,QAAQ,EAAE,MAAM;IAChBtC,KAAK,EAAE,OAAO;IACduC,UAAU,EAAE,GAAG;IACfyG,aAAa,EAAE,QAAQ;IACvBD,SAAS,EAAE;EACb;AACF,CAAC;AAED,eAAe1pB,KAAK;AAAC,IAAAooB,EAAA;AAAA2E,YAAA,CAAA3E,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}